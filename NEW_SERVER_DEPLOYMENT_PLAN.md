# SNK Game Server - New Instance Deployment Plan

## Executive Summary

This document provides a comprehensive plan for deploying a new SNK game server instance. The plan covers database migration, configuration updates, infrastructure setup, deployment procedures, and validation testing.

## Current Environment Analysis

### Target Server Configuration (aaPanel Environment)
- **Server IP**: **************
- **Control Panel**: aaPanel (with pre-installed services)
- **MySQL**: Pre-installed via aaPanel (Password: 6eaef66fd7ed44fa)
- **Redis**: Pre-installed via aaPanel
- **PHP**: Pre-installed via aaPanel
- **SSH Access**: Configured with SSH keys
- **Database Structure**: MySQL with 4 databases (db_snk, db_iap, db_zone_1, db_zone_101)
- **Framework**: SFW (Service Framework) + SNK Game Servers
- **Key Ports**: 2000-2002 (SFW), 3306 (MySQL), 6379 (Redis), 8101/8201/8301 (Game Servers)

### Architecture Components
1. **SFW Framework**: Registry, Control, Agent services
2. **Core Servers**: Account, Login, Platform, IAP servers
3. **Zone Servers**: Gate, Game, Battle servers per zone
4. **Dependencies**: MySQL, Redis, Auto-restart monitoring

---

## 1. DATABASE ANALYSIS & MIGRATION

### 1.1 Database Schema Overview

**Primary Databases:**
- `db_snk`: Registry and server configuration
- `db_iap`: In-app purchase transactions
- `db_zone_1`: Game data for zone 1
- `db_zone_101`: Game data for zone 101

### 1.2 IP Address References in Database

**Critical Tables Requiring Updates:**
- `t_server`: Contains server IP addresses (127.0.0.1 references)
- `t_service`: Service endpoint configurations

**⚠️ CRITICAL IP CONFIGURATION FINDINGS:**

**1. Internal vs External Service Binding:**
- **SFW Framework services** (ports 2000-2002): MUST use `127.0.0.1` (localhost only)
- **Game server internal communication** (8101, 8201, 8301): Use `127.0.0.1` for single-server deployment
- **External client connections** (18101): MUST use `0.0.0.0` for external access
- **Database connections**: Use actual server IP for remote access

**2. Known Issues with 0.0.0.0:**
- `HostIP = 0.0.0.0` in sfwagent.conf CAUSES FAILURE - must be specific IP
- SFW Framework does NOT support 0.0.0.0 binding
- Only use 0.0.0.0 for services that need external client access

**SQL Update Strategy:**
```sql
-- Update server IP references (be careful - some should stay 127.0.0.1)
UPDATE t_server SET node = 'NEW_SERVER_IP' WHERE node = '127.0.0.1' AND server NOT IN ('sfwregistry', 'sfwcontrol');

-- Update service endpoints (preserve localhost for SFW services)
UPDATE t_service SET endpoint = REPLACE(endpoint, '127.0.0.1', 'NEW_SERVER_IP')
WHERE server NOT IN ('sfwregistry', 'sfwcontrol') AND service NOT LIKE '%Agent%';

-- Special case: External client ports need 0.0.0.0
UPDATE t_service SET endpoint = REPLACE(endpoint, '127.0.0.1', '0.0.0.0')
WHERE service = 'HandleConn_1' AND endpoint LIKE '%18101%';
```

### 1.3 Database Migration Options

**Option A: Fresh Database Setup**
- Create new databases using provided SQL dumps
- Update IP references during import
- Suitable for new environments

**Option B: Data Migration**
- Export data from existing server
- Transform IP references during migration
- Import to new server
- Suitable for production data preservation

### 1.4 Database Credentials Management

**Template Variables to Update:**

**⚠️ CRITICAL: IP Configuration Rules**
- `TEMPLATE_ServerIP` → **127.0.0.1** (for sfwagent HostIP - NEVER use 0.0.0.0)
- `TEMPLATE_REGISTRY_ENDPOINT` → **tcp -h 127.0.0.1 -p 2000** (SFW services stay localhost)
- `TEMPLATE_ENDPOINT_HandleConn` → **tcp -h 0.0.0.0 -p 18101** (external client access)
- `TEMPLATE_ENDPOINT_GateServiceObj` → **tcp -h 127.0.0.1 -p 8101** (internal communication)

**Database Configuration:**
- `TEMPLATE_RegistryDbHost` → New server IP (for remote DB access)
- `TEMPLATE_RegistryDbUser` → Database username
- `TEMPLATE_RegistryDbPasswd` → Secure password
- `TEMPLATE_IAPDB_HOST` → New server IP
- `TEMPLATE_IAPDB_USER` → IAP database user
- `TEMPLATE_IAPDB_PASSWD` → IAP database password
- `TEMPLATE_GameDB` → Game database host
- `TEMPLATE_DB_USER` → Game database user
- `TEMPLATE_DB_PASSWD` → Game database password

---

## 2. CONFIGURATION FILE UPDATES

### 2.1 Template Configuration System

The system uses a template-based configuration approach with placeholder variables that must be replaced for each deployment.

### 2.2 Critical Template Variables

**Network & Connectivity:**
- `TEMPLATE_REGISTRY_ENDPOINT` → `tcp -h NEW_SERVER_IP -p 2000`
- `TEMPLATE_SET_DIVISION` → Zone division identifier
- `TEMPLATE_ENDPOINT_*` → Service-specific endpoints

**Database Connections:**
- `TEMPLATE_GameDB` → Game database host IP
- `TEMPLATE_GAME_DBNAME` → Database name (e.g., db_zone_1)
- `TEMPLATE_GAME_DBPORT` → Database port (3306)

**Redis Configuration:**
- `TEMPLATE_Redis_Host` → Redis server IP
- `TEMPLATE_Redis_Port` → Redis port (6379)
- `TEMPLATE_Redis_Password` → Redis authentication

**Game-Specific Settings:**
- `TEMPLATE_GAMEID` → Unique game server ID
- `TEMPLATE_GAMENUM` → Number of game servers
- `TEMPLATE_GATENUM` → Number of gate servers
- `TEMPLATE_GATEID` → Unique gate server ID

### 2.3 Configuration Files Requiring Updates

**SFW Framework:**
- `server/Sfw/src/sfwserver/sfwregistry/sfwregistry.conf`
- `server/Sfw/src/sfwserver/sfwcontrol/sfwcontrol.conf`

**Core Servers:**
- `server/*/Config/*.conf` (All server configuration files)
- `server/*/Config/*.sfw.conf` (SFW-specific configurations)

**GM Tool Configuration:**
- `gmtool.snk.com/gmtool/snk.gmtool.com/phpinc/MTTDConfig.php`
- `gmtool.snk.com/gmtool/snk.gmtool.com/phpinc/env.php`

---

## 3. INFRASTRUCTURE SETUP

### 3.1 Server Requirements

**Minimum Hardware:**
- CPU: 4+ cores
- RAM: 8GB+
- Storage: 100GB+ SSD
- Network: 1Gbps connection

**Operating System:**
- Linux (Ubuntu 18.04+ or CentOS 7+)
- MySQL 5.7+
- Redis 5.0+
- PHP 7.4+ (for GM tools)

### 3.2 Network Configuration

**Required Ports (Inbound):**
- 2000: SFW Registry
- 2001: SFW Control  
- 2002: SFW Agent
- 3306: MySQL Database
- 6379: Redis Cache
- 7011: Account Server
- 7021: Forward Server
- 7091: Friend Server
- 8101: Gate Server (Zone)
- 8201: Game Server (Zone)
- 8301: Battle Server (Zone)
- 17071: Bgip Proxy
- 17101: IAP Server
- 18101: Gate Server (Additional)

**Firewall Rules:**
```bash
# Allow SSH
ufw allow 22/tcp

# Allow game server ports
ufw allow 2000:2002/tcp
ufw allow 3306/tcp
ufw allow 6379/tcp
ufw allow 7011/tcp
ufw allow 7021/tcp
ufw allow 7091/tcp
ufw allow 8101/tcp
ufw allow 8201/tcp
ufw allow 8301/tcp
ufw allow 17071/tcp
ufw allow 17101/tcp
ufw allow 18101/tcp

# Enable firewall
ufw enable
```

### 3.3 Directory Structure

**Required Directories:**
```
/data/
├── SNK/                 # Game server binaries and configs
├── sfw/                 # SFW framework binaries
├── applog/              # Application logs
│   ├── SNK/
│   ├── sfw/
│   ├── ta_log/
│   └── auto_restart/
└── backup/              # Backup storage
```

---

## 4. DEPLOYMENT PROCESS

### 4.1 Pre-Deployment Checklist

**Infrastructure Preparation:**
- [ ] Server provisioned with required specifications
- [ ] Operating system installed and updated
- [ ] Network configuration completed
- [ ] Firewall rules configured
- [ ] SSH access established

**Software Dependencies:**
- [ ] MySQL 5.7+ installed and configured
- [ ] Redis installed and configured
- [ ] Required system libraries installed
- [ ] Directory structure created

### 4.2 Database Setup

**Step 1: Install and Configure MySQL**
```bash
# Install MySQL
apt-get update
apt-get install mysql-server-5.7

# Secure installation
mysql_secure_installation

# Create databases
mysql -u root -p << EOF
CREATE DATABASE db_snk CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE db_iap CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE db_zone_1 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE db_zone_101 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# Create users and grant permissions
CREATE USER 'snk_user'@'localhost' IDENTIFIED BY 'SECURE_PASSWORD';
GRANT ALL PRIVILEGES ON db_snk.* TO 'snk_user'@'localhost';
GRANT ALL PRIVILEGES ON db_iap.* TO 'snk_user'@'localhost';
GRANT ALL PRIVILEGES ON db_zone_*.* TO 'snk_user'@'localhost';
FLUSH PRIVILEGES;
EOF
```

**Step 2: Import Database Schema**
```bash
# Import database dumps (update IP addresses during import)
sed 's/127\.0\.0\.1/NEW_SERVER_IP/g' db_snk_*.sql | mysql -u snk_user -p db_snk
sed 's/127\.0\.0\.1/NEW_SERVER_IP/g' db_iap_*.sql | mysql -u snk_user -p db_iap
sed 's/127\.0\.0\.1/NEW_SERVER_IP/g' db_zone_1_*.sql | mysql -u snk_user -p db_zone_1
sed 's/127\.0\.0\.1/NEW_SERVER_IP/g' db_zone_101_*.sql | mysql -u snk_user -p db_zone_101
```

### 4.3 Redis Setup

```bash
# Install Redis
apt-get install redis-server

# Configure Redis
echo "bind NEW_SERVER_IP 127.0.0.1" >> /etc/redis/redis.conf
echo "requirepass REDIS_PASSWORD" >> /etc/redis/redis.conf

# Start Redis
systemctl enable redis-server
systemctl start redis-server
```

### 4.4 Configuration Template Processing

**Step 1: Create Configuration Processing Script**
```bash
#!/bin/bash
# config_processor.sh - Process template configurations

NEW_SERVER_IP="YOUR_NEW_SERVER_IP"
REDIS_PASSWORD="YOUR_REDIS_PASSWORD"
DB_PASSWORD="YOUR_DB_PASSWORD"
GAME_ID="101"  # Unique game server ID
ZONE_ID="101"  # Zone identifier

# Function to process template files
process_template() {
    local template_file="$1"
    local output_file="$2"

    sed -e "s/TEMPLATE_REGISTRY_ENDPOINT/tcp -h $NEW_SERVER_IP -p 2000/g" \
        -e "s/TEMPLATE_RegistryDbHost/$NEW_SERVER_IP/g" \
        -e "s/TEMPLATE_RegistryDbUser/snk_user/g" \
        -e "s/TEMPLATE_RegistryDbPasswd/$DB_PASSWORD/g" \
        -e "s/TEMPLATE_GameDB/$NEW_SERVER_IP/g" \
        -e "s/TEMPLATE_GAME_DBNAME/db_zone_$ZONE_ID/g" \
        -e "s/TEMPLATE_GAME_DBPORT/3306/g" \
        -e "s/TEMPLATE_DB_USER/snk_user/g" \
        -e "s/TEMPLATE_DB_PASSWD/$DB_PASSWORD/g" \
        -e "s/TEMPLATE_Redis_Host/$NEW_SERVER_IP/g" \
        -e "s/TEMPLATE_Redis_Port/6379/g" \
        -e "s/TEMPLATE_Redis_Password/$REDIS_PASSWORD/g" \
        -e "s/TEMPLATE_GAMEID/$GAME_ID/g" \
        -e "s/TEMPLATE_GAMENUM/1/g" \
        -e "s/TEMPLATE_GATENUM/1/g" \
        -e "s/TEMPLATE_GATEID/1/g" \
        -e "s/TEMPLATE_SET_DIVISION/snk.zone.$ZONE_ID/g" \
        -e "s/TEMPLATE_ENDPOINT_GameServiceObj/tcp -h $NEW_SERVER_IP -p 8201/g" \
        -e "s/TEMPLATE_ENDPOINT_LoginServiceObj/tcp -h $NEW_SERVER_IP -p 7001/g" \
        -e "s/TEMPLATE_ENDPOINT_AccountServiceObj/tcp -h $NEW_SERVER_IP -p 7011/g" \
        -e "s/TEMPLATE_ENDPOINT_GVGServiceObj/tcp -h $NEW_SERVER_IP -p 7081/g" \
        -e "s/TEMPLATE_ENDPOINT_MatchServiceObj/tcp -h $NEW_SERVER_IP -p 7051/g" \
        -e "s/TEMPLATE_ENDPOINT_GlobalServiceObj/tcp -h $NEW_SERVER_IP -p 7031/g" \
        -e "s/TEMPLATE_ENDPOINT_TeamServiceObj/tcp -h $NEW_SERVER_IP -p 7061/g" \
        -e "s/TEMPLATE_ENDPOINT_PlayServiceObj/tcp -h $NEW_SERVER_IP -p 7071/g" \
        -e "s/TEMPLATE_IAPDB_HOST/$NEW_SERVER_IP/g" \
        -e "s/TEMPLATE_IAPDB_USER/snk_user/g" \
        -e "s/TEMPLATE_IAPDB_PASSWD/$DB_PASSWORD/g" \
        "$template_file" > "$output_file"
}

# Process all template configurations
find /data/server/ConfTemplate -name "*.conf" -o -name "*.sfw.conf" | while read template; do
    relative_path=${template#/data/server/ConfTemplate/}
    output_path="/data/server/${relative_path}"
    mkdir -p "$(dirname "$output_path")"
    process_template "$template" "$output_path"
    echo "Processed: $template -> $output_path"
done
```

### 4.5 Application Deployment

**Step 1: Deploy Server Binaries**
```bash
# Copy server binaries to target directories
cp -r server/build/* /data/SNK/
cp -r server/Sfw/build/* /data/sfw/

# Set proper permissions
chmod +x /data/SNK/*
chmod +x /data/sfw/*
chown -R root:root /data/SNK /data/sfw
```

**Step 2: Deploy Management Scripts**
```bash
# Copy management scripts
cp data/system_start.sh /data/
cp data/system_stop.sh /data/
cp data/game_status_dashboard.sh /data/
cp data/SNK/*.sh /data/SNK/
cp data/sfw/*.sh /data/sfw/

# Set executable permissions
chmod +x /data/*.sh
chmod +x /data/SNK/*.sh
chmod +x /data/sfw/*.sh
```

**Step 3: Deploy GM Tools (Optional)**
```bash
# Deploy GM tools if web interface is needed
cp -r gmtool.snk.com /var/www/html/
chown -R www-data:www-data /var/www/html/gmtool.snk.com

# Update GM tool configuration
sed -i "s/127\.0\.0\.1/$NEW_SERVER_IP/g" /var/www/html/gmtool.snk.com/gmtool/snk.gmtool.com/phpinc/MTTDConfig.php
```

---

## 5. TESTING & VALIDATION

### 5.1 Pre-Start Validation

**Configuration Validation:**
```bash
# Verify configuration files syntax
find /data/server -name "*.conf" | xargs -I {} bash -c 'echo "Checking: {}" && grep -q "TEMPLATE_" {} && echo "ERROR: Unprocessed templates found" || echo "OK"'

# Verify database connectivity
mysql -h $NEW_SERVER_IP -u snk_user -p$DB_PASSWORD -e "SHOW DATABASES;"

# Verify Redis connectivity
redis-cli -h $NEW_SERVER_IP -p 6379 -a $REDIS_PASSWORD ping
```

### 5.2 Service Startup Testing

**Step 1: Start SFW Framework**
```bash
cd /data/sfw
./start_manual.sh

# Verify SFW services
sleep 10
netstat -tuln | grep -E "(2000|2001|2002)"
```

**Step 2: Start Game Servers**
```bash
cd /data/SNK
./start_manual.sh

# Verify game services
sleep 30
netstat -tuln | grep -E "(8101|8201|8301)"
```

### 5.3 Functional Testing

**Database Connectivity Test:**
```bash
# Test database operations
mysql -h $NEW_SERVER_IP -u snk_user -p$DB_PASSWORD db_snk -e "SELECT * FROM t_server LIMIT 5;"
```

**Service Communication Test:**
```bash
# Test SFW registry
telnet $NEW_SERVER_IP 2000

# Test game server ports
for port in 8101 8201 8301; do
    echo "Testing port $port..."
    timeout 5 bash -c "</dev/tcp/$NEW_SERVER_IP/$port" && echo "Port $port: OK" || echo "Port $port: FAILED"
done
```

### 5.4 Load Testing (Optional)

**Connection Load Test:**
```bash
# Test concurrent connections
for i in {1..100}; do
    (timeout 1 bash -c "</dev/tcp/$NEW_SERVER_IP/8101" &)
done
wait
```

### 5.5 Monitoring Setup

**System Monitoring:**
```bash
# Setup auto-restart service
cp data/auto_restart.service /etc/systemd/system/
systemctl enable auto_restart
systemctl start auto_restart

# Verify monitoring
systemctl status auto_restart
```

---

## 6. POST-DEPLOYMENT PROCEDURES

### 6.1 Backup Configuration

**Create Initial Backup:**
```bash
# Backup configurations
tar -czf /data/backup/initial_config_$(date +%Y%m%d_%H%M%S).tar.gz \
    /data/server/*/Config/ \
    /data/SNK/ \
    /data/sfw/

# Backup databases
mysqldump -h $NEW_SERVER_IP -u snk_user -p$DB_PASSWORD --all-databases > \
    /data/backup/initial_db_$(date +%Y%m%d_%H%M%S).sql
```

### 6.2 Documentation Updates

**Update Server Documentation:**
- Record new server IP address
- Document configuration changes
- Update network diagrams
- Record database credentials (securely)

### 6.3 Monitoring Integration

**Setup Log Monitoring:**
```bash
# Configure log rotation
cat > /etc/logrotate.d/snk-game << EOF
/data/applog/SNK/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF
```

---

## 7. ROLLBACK PROCEDURES

### 7.1 Service Rollback

**Emergency Stop:**
```bash
# Stop all services
/data/system_stop.sh

# Restore from backup if needed
tar -xzf /data/backup/initial_config_TIMESTAMP.tar.gz -C /
```

### 7.2 Database Rollback

**Database Restoration:**
```bash
# Stop services
/data/system_stop.sh

# Restore database
mysql -h $NEW_SERVER_IP -u snk_user -p$DB_PASSWORD < /data/backup/initial_db_TIMESTAMP.sql

# Restart services
/data/system_start.sh
```

---

## 8. MAINTENANCE PROCEDURES

### 8.1 Regular Maintenance

**Daily Tasks:**
- Monitor service status via `/data/game_status_dashboard.sh`
- Check log files for errors
- Verify database connectivity

**Weekly Tasks:**
- Review system performance
- Update security patches
- Backup configurations and data

**Monthly Tasks:**
- Full system backup
- Performance optimization review
- Security audit

### 8.2 Troubleshooting Guide

**Common Issues:**

1. **Service Won't Start:**
   - Check configuration files for template variables
   - Verify database connectivity
   - Check port conflicts

2. **Database Connection Errors:**
   - Verify credentials
   - Check firewall rules
   - Test network connectivity

3. **Performance Issues:**
   - Monitor resource usage
   - Check log files for bottlenecks
   - Review configuration parameters

---

## 9. SECURITY CONSIDERATIONS

### 9.1 Network Security

- Configure firewall rules to restrict access
- Use VPN for administrative access
- Implement fail2ban for SSH protection
- Regular security updates

### 9.2 Database Security

- Use strong passwords
- Limit database user privileges
- Enable SSL connections
- Regular security audits

### 9.3 Application Security

- Secure configuration files
- Regular log monitoring
- Access control implementation
- Security patch management

---

## 10. CONCLUSION

This deployment plan provides a comprehensive approach to setting up a new SNK game server instance. Following these procedures ensures:

- Proper database migration and configuration
- Correct service configuration and deployment
- Comprehensive testing and validation
- Robust monitoring and maintenance procedures
- Security best practices implementation

**Success Criteria:**
- All services start successfully
- Database connectivity confirmed
- Game functionality verified
- Monitoring systems operational
- Documentation updated

**Next Steps After Deployment:**
1. Conduct user acceptance testing
2. Implement monitoring alerts
3. Schedule regular maintenance
4. Plan capacity scaling if needed
5. Document lessons learned
