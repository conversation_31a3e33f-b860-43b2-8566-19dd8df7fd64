-- MySQL dump 10.13  Distrib 5.7.40, for Linux (x86_64)
--
-- Host: localhost    Database: db_zone_1
-- ------------------------------------------------------
-- Server version	5.7.40-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `t_guild`
--

DROP TABLE IF EXISTS `t_guild`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_guild` (
  `guild_id` bigint(20) unsigned NOT NULL,
  `bin_data` mediumblob,
  `guild_name` varchar(30) DEFAULT NULL,
  PRIMARY KEY (`guild_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_guild`
--

LOCK TABLES `t_guild` WRITE;
/*!40000 ALTER TABLE `t_guild` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_guild` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_misc`
--

DROP TABLE IF EXISTS `t_misc`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_misc` (
  `id` varchar(255) NOT NULL,
  `bin_data` mediumblob,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_misc`
--

LOCK TABLES `t_misc` WRITE;
/*!40000 ALTER TABLE `t_misc` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_misc` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_offline`
--

DROP TABLE IF EXISTS `t_offline`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_offline` (
  `role_id` bigint(20) unsigned NOT NULL,
  `bin_data` mediumblob,
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_offline`
--

LOCK TABLES `t_offline` WRITE;
/*!40000 ALTER TABLE `t_offline` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_offline` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_role`
--

DROP TABLE IF EXISTS `t_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_role` (
  `roleid` bigint(20) unsigned NOT NULL,
  `accountid` bigint(20) unsigned NOT NULL,
  `bin_data` mediumblob,
  `name` tinyblob,
  `level` int(10) unsigned NOT NULL DEFAULT '0',
  `reg_time` datetime DEFAULT NULL,
  `last_login_time` datetime DEFAULT NULL,
  `this_login_time` datetime DEFAULT NULL,
  `online_total_time` int(10) unsigned NOT NULL DEFAULT '0',
  `total_login_days` int(10) unsigned NOT NULL DEFAULT '0',
  `continuous_login_days` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`roleid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_role`
--

LOCK TABLES `t_role` WRITE;
/*!40000 ALTER TABLE `t_role` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'db_zone_1'
--

--
-- Dumping routines for database 'db_zone_1'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-25 21:14:53
