-- SNK Game Server Database Migration Script (CORRECTED)
-- This script properly handles IP address migration while preserving SFW Framework requirements
-- 
-- CRITICAL: SFW Framework services MUST remain on 127.0.0.1
-- Only game services can be moved to new server IP

-- Variables (replace these with actual values)
-- SET @NEW_SERVER_IP = '*************';  -- Replace with actual new server IP

-- =============================================================================
-- STEP 1: BACKUP CURRENT CONFIGURATION
-- =============================================================================

-- Create backup tables
CREATE TABLE IF NOT EXISTS t_server_backup AS SELECT * FROM t_server;
CREATE TABLE IF NOT EXISTS t_service_backup AS SELECT * FROM t_service;

-- =============================================================================
-- STEP 2: UPDATE SERVER REGISTRATIONS (SELECTIVE)
-- =============================================================================

-- Update NON-SFW servers to new IP
-- CRITICAL: Exclude sfwregistry and sfwcontrol - they MUST stay on 127.0.0.1
UPDATE t_server 
SET node = @NEW_SERVER_IP 
WHERE node = '127.0.0.1' 
AND server NOT IN ('sfwregistry', 'sfwcontrol');

-- Verify SFW servers remain on localhost
SELECT 'SFW Server Check' as check_type, server, node 
FROM t_server 
WHERE server IN ('sfwregistry', 'sfwcontrol');
-- Expected: All should show node = '127.0.0.1'

-- =============================================================================
-- STEP 3: UPDATE SERVICE ENDPOINTS (SELECTIVE)
-- =============================================================================

-- Update game service endpoints to new server IP
-- CRITICAL: Exclude SFW services and agent-related services
UPDATE t_service 
SET endpoint = REPLACE(endpoint, '127.0.0.1', @NEW_SERVER_IP),
    node = @NEW_SERVER_IP
WHERE server NOT IN ('sfwregistry', 'sfwcontrol') 
AND service NOT LIKE '%Agent%'
AND endpoint LIKE '%127.0.0.1%';

-- =============================================================================
-- STEP 4: SPECIAL HANDLING FOR EXTERNAL CLIENT ACCESS
-- =============================================================================

-- Update external client connection ports to bind on all interfaces
-- This allows Unity clients to connect from external networks
UPDATE t_service 
SET endpoint = REPLACE(endpoint, '127.0.0.1', '0.0.0.0')
WHERE service = 'HandleConn_1' 
AND endpoint LIKE '%18101%';

-- Verify external client port configuration
SELECT 'External Client Port Check' as check_type, server, service, endpoint 
FROM t_service 
WHERE service = 'HandleConn_1' AND endpoint LIKE '%18101%';
-- Expected: Should show 0.0.0.0:18101

-- =============================================================================
-- STEP 5: VERIFICATION QUERIES
-- =============================================================================

-- Check 1: Verify SFW Framework services remain on localhost
SELECT 'SFW Services Check' as check_type, server, service, node, endpoint 
FROM t_service 
WHERE server IN ('sfwregistry', 'sfwcontrol')
ORDER BY server, service;
-- Expected: All should have node='127.0.0.1' and endpoint with '127.0.0.1'

-- Check 2: Verify game services moved to new IP
SELECT 'Game Services Check' as check_type, server, service, node, 
       CASE 
           WHEN endpoint LIKE '%127.0.0.1%' THEN 'LOCALHOST'
           WHEN endpoint LIKE CONCAT('%', @NEW_SERVER_IP, '%') THEN 'NEW_IP'
           WHEN endpoint LIKE '%0.0.0.0%' THEN 'ALL_INTERFACES'
           ELSE 'OTHER'
       END as endpoint_type,
       endpoint
FROM t_service 
WHERE server NOT IN ('sfwregistry', 'sfwcontrol')
ORDER BY server, service;

-- Check 3: Count services by IP type
SELECT 'Service Count by IP' as check_type,
       CASE 
           WHEN endpoint LIKE '%127.0.0.1%' THEN 'LOCALHOST'
           WHEN endpoint LIKE CONCAT('%', @NEW_SERVER_IP, '%') THEN 'NEW_IP'
           WHEN endpoint LIKE '%0.0.0.0%' THEN 'ALL_INTERFACES'
           ELSE 'OTHER'
       END as ip_type,
       COUNT(*) as service_count
FROM t_service 
GROUP BY ip_type
ORDER BY ip_type;

-- Check 4: Identify any potential issues
SELECT 'Potential Issues' as check_type, server, service, endpoint
FROM t_service 
WHERE (server IN ('sfwregistry', 'sfwcontrol') AND endpoint NOT LIKE '%127.0.0.1%')
   OR (service LIKE '%Agent%' AND endpoint NOT LIKE '%127.0.0.1%')
ORDER BY server, service;
-- Expected: Should return no rows

-- =============================================================================
-- STEP 6: ROLLBACK SCRIPT (IF NEEDED)
-- =============================================================================

/*
-- ROLLBACK COMMANDS (uncomment if rollback is needed)

-- Restore from backup
DELETE FROM t_server;
INSERT INTO t_server SELECT * FROM t_server_backup;

DELETE FROM t_service;  
INSERT INTO t_service SELECT * FROM t_service_backup;

-- Drop backup tables
DROP TABLE t_server_backup;
DROP TABLE t_service_backup;
*/

-- =============================================================================
-- STEP 7: FINAL VALIDATION
-- =============================================================================

-- Final check: Ensure critical services are properly configured
SELECT 'Final Validation' as check_type,
       'SFW Registry' as service_type,
       COUNT(*) as count
FROM t_service 
WHERE server = 'sfwregistry' AND endpoint LIKE '%127.0.0.1:2000%'

UNION ALL

SELECT 'Final Validation' as check_type,
       'SFW Control' as service_type,
       COUNT(*) as count
FROM t_service 
WHERE server = 'sfwcontrol' AND endpoint LIKE '%127.0.0.1:2001%'

UNION ALL

SELECT 'Final Validation' as check_type,
       'External Client Port' as service_type,
       COUNT(*) as count
FROM t_service 
WHERE service = 'HandleConn_1' AND endpoint LIKE '%0.0.0.0:18101%'

UNION ALL

SELECT 'Final Validation' as check_type,
       'Game Services on New IP' as service_type,
       COUNT(*) as count
FROM t_service 
WHERE server NOT IN ('sfwregistry', 'sfwcontrol') 
AND service NOT LIKE '%Agent%'
AND service != 'HandleConn_1'
AND endpoint LIKE CONCAT('%', @NEW_SERVER_IP, '%');

-- Expected results:
-- SFW Registry: 1
-- SFW Control: 1  
-- External Client Port: 1 (if configured)
-- Game Services on New IP: > 0

-- =============================================================================
-- USAGE INSTRUCTIONS
-- =============================================================================

/*
To use this script:

1. Set the new server IP:
   SET @NEW_SERVER_IP = '*************';  -- Replace with actual IP

2. Run the entire script

3. Verify results using the validation queries

4. If issues found, use rollback commands

5. After successful migration, clean up:
   DROP TABLE t_server_backup;
   DROP TABLE t_service_backup;

CRITICAL REMINDERS:
- SFW Framework services MUST stay on 127.0.0.1
- Only external client ports should use 0.0.0.0
- Game services can use new server IP for internal communication
- Always backup before running migration
- Test thoroughly after migration
*/
