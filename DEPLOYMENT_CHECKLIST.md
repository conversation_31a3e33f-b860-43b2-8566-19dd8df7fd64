# SNK Game Server - New Instance Deployment Checklist

## Pre-Deployment Phase

### Infrastructure Preparation
- [ ] Server provisioned with minimum requirements (4+ cores, 8GB+ RAM, 100GB+ SSD)
- [ ] Operating system installed (Ubuntu 18.04+ or CentOS 7+)
- [ ] Network connectivity established
- [ ] SSH access configured
- [ ] Firewall rules configured for required ports
- [ ] DNS/hostname configured

### Software Dependencies
- [ ] MySQL 5.7+ installed and secured
- [ ] Redis 5.0+ installed and configured
- [ ] PHP 7.4+ installed (if GM tools needed)
- [ ] Required system libraries installed
- [ ] Directory structure created (/data/SNK, /data/sfw, /data/applog)

### Security Setup
- [ ] Strong passwords generated for database users
- [ ] Redis authentication configured
- [ ] SSH key-based authentication enabled
- [ ] Fail2ban configured for SSH protection
- [ ] System updates applied

## Database Setup Phase

### Database Installation
- [ ] MySQL service started and enabled
- [ ] Root password set and secured
- [ ] Database users created with appropriate privileges
- [ ] Required databases created (db_snk, db_iap, db_zone_1, db_zone_101)

### Database Migration
- [ ] SQL dump files prepared with IP address updates
- [ ] Database schema imported successfully
- [ ] IP address references updated in t_server table
- [ ] Service endpoint references updated in t_service table
- [ ] Database connectivity tested from application user

### Database Validation
- [ ] All databases accessible
- [ ] Table structures verified
- [ ] Sample queries executed successfully
- [ ] User permissions validated

## Configuration Phase

### Template Processing
- [ ] New server IP address defined
- [ ] Database credentials configured
- [ ] Redis credentials configured
- [ ] Game-specific IDs assigned (GAME_ID, ZONE_ID, GATE_ID)
- [ ] Configuration processing script created and tested

### Configuration File Updates
- [ ] SFW framework configurations processed
- [ ] Game server configurations processed
- [ ] Database connection strings updated
- [ ] Redis connection parameters updated
- [ ] Service endpoints configured
- [ ] All TEMPLATE_ placeholders replaced

### Configuration Validation
- [ ] Configuration files syntax validated
- [ ] No unprocessed template variables remain
- [ ] File permissions set correctly
- [ ] Configuration backup created

## Application Deployment Phase

### Binary Deployment
- [ ] Server binaries copied to /data/SNK/
- [ ] SFW framework binaries copied to /data/sfw/
- [ ] Management scripts deployed
- [ ] Executable permissions set
- [ ] File ownership configured

### GM Tools Deployment (Optional)
- [ ] GM tools copied to web directory
- [ ] Web server configured
- [ ] GM tool configurations updated
- [ ] Web interface accessibility tested

### Service Configuration
- [ ] Auto-restart service configured
- [ ] Log rotation configured
- [ ] Monitoring scripts deployed
- [ ] Backup scripts configured

## Testing Phase

### Pre-Start Validation
- [ ] Configuration files validated
- [ ] Database connectivity confirmed
- [ ] Redis connectivity confirmed
- [ ] Port availability verified
- [ ] Log directories accessible

### Service Startup Testing
- [ ] SFW framework started successfully
- [ ] SFW services listening on ports 2000-2002
- [ ] Game servers started successfully
- [ ] Game services listening on assigned ports
- [ ] All processes running without errors

### Functional Testing
- [ ] Database operations tested
- [ ] Service communication verified
- [ ] Port connectivity confirmed
- [ ] Log files generating correctly
- [ ] Status dashboard functional

### Load Testing (Optional)
- [ ] Connection load test performed
- [ ] Performance benchmarks recorded
- [ ] Resource usage monitored
- [ ] Stability test completed

## Post-Deployment Phase

### Backup Creation
- [ ] Initial configuration backup created
- [ ] Database backup completed
- [ ] Backup verification performed
- [ ] Backup storage configured

### Documentation Updates
- [ ] Server details documented
- [ ] Configuration changes recorded
- [ ] Network diagrams updated
- [ ] Access credentials documented (securely)
- [ ] Troubleshooting guide updated

### Monitoring Setup
- [ ] Auto-restart monitor enabled
- [ ] Log monitoring configured
- [ ] Performance monitoring setup
- [ ] Alert thresholds configured
- [ ] Monitoring dashboard accessible

### Security Hardening
- [ ] Unnecessary services disabled
- [ ] Security patches applied
- [ ] Access controls verified
- [ ] Audit logging enabled
- [ ] Security scan performed

## Validation & Sign-off

### System Validation
- [ ] All critical services operational
- [ ] Database functionality confirmed
- [ ] Game functionality verified
- [ ] Performance within acceptable limits
- [ ] Security measures validated

### User Acceptance
- [ ] System administrator training completed
- [ ] Documentation handover completed
- [ ] Support procedures established
- [ ] Escalation procedures defined
- [ ] Go-live approval obtained

### Final Checks
- [ ] Rollback procedures tested
- [ ] Emergency contacts updated
- [ ] Maintenance schedule established
- [ ] Capacity planning reviewed
- [ ] Success criteria met

## Emergency Procedures

### Rollback Checklist
- [ ] Stop all services: `/data/system_stop.sh`
- [ ] Restore configuration from backup
- [ ] Restore database from backup
- [ ] Restart services: `/data/system_start.sh`
- [ ] Verify rollback success
- [ ] Document issues encountered

### Emergency Contacts
- [ ] System Administrator: _______________
- [ ] Database Administrator: _______________
- [ ] Network Administrator: _______________
- [ ] Application Support: _______________
- [ ] Management Escalation: _______________

## Key Commands Reference

### Service Management
```bash
# Start entire system
/data/system_start.sh

# Stop entire system
/data/system_stop.sh

# Check system status
/data/game_status_dashboard.sh

# Start SFW framework only
cd /data/sfw && ./start_manual.sh

# Start game servers only
cd /data/SNK && ./start_manual.sh
```

### Monitoring Commands
```bash
# Check service ports
netstat -tuln | grep -E "(2000|2001|2002|8101|8201|8301)"

# Check processes
ps aux | grep -E "(sfw|Server)" | grep -v grep

# Check logs
tail -f /data/applog/SNK/*.log
tail -f /data/applog/sfw/*.log
```

### Database Commands
```bash
# Test database connectivity
mysql -h NEW_SERVER_IP -u snk_user -pPASSWORD -e "SHOW DATABASES;"

# Check server registrations
mysql -h NEW_SERVER_IP -u snk_user -pPASSWORD db_snk -e "SELECT * FROM t_server;"
```

## Deployment Sign-off

**Deployment Team:**
- [ ] System Administrator: _________________ Date: _______
- [ ] Database Administrator: _________________ Date: _______
- [ ] Application Administrator: _________________ Date: _______
- [ ] Security Administrator: _________________ Date: _______
- [ ] Project Manager: _________________ Date: _______

**Deployment Status:**
- [ ] Successful - System ready for production
- [ ] Successful with issues - Issues documented below
- [ ] Failed - Rollback completed

**Issues/Notes:**
_________________________________________________
_________________________________________________
_________________________________________________

**Next Steps:**
_________________________________________________
_________________________________________________
_________________________________________________
