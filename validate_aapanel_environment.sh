#!/bin/bash

# aaPanel Environment Validation Script
# Validates the target server environment before deployment
# Server: **************

set -e

# Server Configuration
SERVER_IP="**************"
SERVER_USER="root"
MYSQL_PASSWORD="6eaef66fd7ed44fa"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to execute command on remote server
remote_exec() {
    local command="$1"
    ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "$command"
}

log_message "${BLUE}========================================${NC}"
log_message "${BLUE}   aaPanel Environment Validation      ${NC}"
log_message "${BLUE}========================================${NC}"

# Test 1: Basic connectivity
log_message "${YELLOW}=== Test 1: Basic Connectivity ===${NC}"

log_message "Testing ping connectivity..."
if ping -c 3 "$SERVER_IP" >/dev/null 2>&1; then
    log_message "${GREEN}✓ Ping successful${NC}"
else
    log_message "${RED}✗ Ping failed${NC}"
    exit 1
fi

log_message "Testing SSH connectivity..."
if ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "echo 'SSH OK'" >/dev/null 2>&1; then
    log_message "${GREEN}✓ SSH connection successful${NC}"
else
    log_message "${RED}✗ SSH connection failed${NC}"
    exit 1
fi

# Test 2: System Information
log_message "${YELLOW}=== Test 2: System Information ===${NC}"

log_message "Gathering system information..."
remote_exec "
echo 'OS Information:'
cat /etc/os-release | head -3

echo 'System Resources:'
echo 'CPU:' \$(nproc) 'cores'
echo 'Memory:' \$(free -h | grep Mem | awk '{print \$2}')
echo 'Disk:' \$(df -h / | tail -1 | awk '{print \$4}') 'available'

echo 'Current Time:' \$(date)
echo 'Uptime:' \$(uptime | awk '{print \$3, \$4}' | sed 's/,//')
"

# Test 3: aaPanel Services
log_message "${YELLOW}=== Test 3: aaPanel Services ===${NC}"

log_message "Checking aaPanel installation..."
remote_exec "
if [ -d '/www/server' ]; then
    echo '✓ aaPanel directory found'
    ls -la /www/server/ | grep -E '(mysql|redis|php|nginx|apache)'
else
    echo '✗ aaPanel directory not found'
fi

echo 'Checking aaPanel processes:'
ps aux | grep -E '(bt|panel)' | grep -v grep | head -3 || echo 'aaPanel processes not found'
"

# Test 4: MySQL Service
log_message "${YELLOW}=== Test 4: MySQL Service ===${NC}"

log_message "Testing MySQL connectivity..."
remote_exec "
echo 'MySQL Service Status:'
systemctl is-active mysql 2>/dev/null || systemctl is-active mysqld 2>/dev/null || echo 'MySQL service status unknown'

echo 'Testing MySQL connection:'
mysql -u root -p'$MYSQL_PASSWORD' -e 'SELECT VERSION();' 2>/dev/null && echo '✓ MySQL connection successful' || echo '✗ MySQL connection failed'

echo 'MySQL Process:'
ps aux | grep mysql | grep -v grep | head -1 || echo 'MySQL process not found'

echo 'MySQL Port:'
netstat -tuln | grep 3306 || echo 'MySQL port 3306 not listening'
"

# Test 5: Redis Service
log_message "${YELLOW}=== Test 5: Redis Service ===${NC}"

log_message "Testing Redis service..."
remote_exec "
echo 'Redis Service Status:'
systemctl is-active redis 2>/dev/null || systemctl is-active redis-server 2>/dev/null || echo 'Redis service status unknown'

echo 'Testing Redis connection:'
redis-cli ping 2>/dev/null && echo '✓ Redis connection successful' || echo '✗ Redis connection failed (may need password)'

echo 'Redis Process:'
ps aux | grep redis | grep -v grep | head -1 || echo 'Redis process not found'

echo 'Redis Port:'
netstat -tuln | grep 6379 || echo 'Redis port 6379 not listening'
"

# Test 6: PHP Installation
log_message "${YELLOW}=== Test 6: PHP Installation ===${NC}"

log_message "Checking PHP installation..."
remote_exec "
echo 'PHP Version:'
php -v | head -1 2>/dev/null || echo 'PHP not found in PATH'

echo 'PHP Extensions:'
php -m | grep -E '(mysql|redis|json|curl)' 2>/dev/null || echo 'PHP extensions check failed'

echo 'PHP Configuration:'
php -i | grep -E '(memory_limit|max_execution_time)' | head -2 2>/dev/null || echo 'PHP config check failed'
"

# Test 7: Network and Firewall
log_message "${YELLOW}=== Test 7: Network and Firewall ===${NC}"

log_message "Checking network configuration..."
remote_exec "
echo 'Network Interfaces:'
ip addr show | grep -E '(inet|UP)' | head -5

echo 'Firewall Status:'
ufw status 2>/dev/null || firewall-cmd --state 2>/dev/null || iptables -L | head -3 2>/dev/null || echo 'Firewall status unknown'

echo 'Open Ports:'
netstat -tuln | grep LISTEN | head -10
"

# Test 8: Disk Space and Permissions
log_message "${YELLOW}=== Test 8: Disk Space and Permissions ===${NC}"

log_message "Checking disk space and permissions..."
remote_exec "
echo 'Disk Usage:'
df -h | grep -E '(Filesystem|/dev/)' | head -5

echo 'Available Space in /:'
df -h / | tail -1 | awk '{print \"Available: \" \$4 \" (\" \$5 \" used)\"}'

echo 'Testing write permissions:'
mkdir -p /tmp/snk_test && echo 'test' > /tmp/snk_test/write_test.txt && rm -rf /tmp/snk_test && echo '✓ Write permissions OK' || echo '✗ Write permissions failed'

echo 'Current user:'
whoami
echo 'User groups:'
groups
"

# Test 9: Required Ports Availability
log_message "${YELLOW}=== Test 9: Required Ports Availability ===${NC}"

log_message "Checking required ports..."
remote_exec "
echo 'Checking game server ports:'
REQUIRED_PORTS=(2000 2001 2002 7011 7021 7031 7041 7051 7061 7071 7081 7091 7101 7201 8101 8201 8301 17071 17101 18101)

for port in \${REQUIRED_PORTS[@]}; do
    if netstat -tuln | grep -q \":\$port \"; then
        echo \"⚠ Port \$port is already in use\"
    else
        echo \"✓ Port \$port is available\"
    fi
done
"

# Test 10: System Dependencies
log_message "${YELLOW}=== Test 10: System Dependencies ===${NC}"

log_message "Checking system dependencies..."
remote_exec "
echo 'Required commands:'
COMMANDS=(gcc g++ make cmake tar gzip wget curl unzip)

for cmd in \${COMMANDS[@]}; do
    if command -v \$cmd >/dev/null 2>&1; then
        echo \"✓ \$cmd is available\"
    else
        echo \"✗ \$cmd is missing\"
    fi
done

echo 'System libraries:'
ldconfig -p | grep -E '(libssl|libcrypto|libmysql|libz)' | wc -l | awk '{print \"Found \" \$1 \" required libraries\"}'
"

# Summary
log_message "${BLUE}========================================${NC}"
log_message "${BLUE}        Validation Summary              ${NC}"
log_message "${BLUE}========================================${NC}"

log_message "${GREEN}Environment validation completed!${NC}"
log_message ""
log_message "${BLUE}Server Information:${NC}"
log_message "• IP: $SERVER_IP"
log_message "• User: $SERVER_USER"
log_message "• Panel: aaPanel"
log_message "• MySQL Password: [CONFIGURED]"
log_message ""
log_message "${BLUE}Next Steps:${NC}"
log_message "1. Review validation results above"
log_message "2. Fix any issues found"
log_message "3. Run deployment script: ./deploy_to_aapanel_server.sh"
log_message ""
log_message "${YELLOW}Note: Some warnings are normal and will be handled during deployment${NC}"

log_message "${GREEN}✅ Validation completed successfully!${NC}"
