#!/bin/bash

# SNK Game Server Deployment Script for aaPanel Environment
# Target Server: ************** (aaPanel with pre-installed services)
# Author: Auto-generated for SNK Game Management

set -e

# Server Configuration
SERVER_IP="**************"
SERVER_USER="root"
MYSQL_PASSWORD="6eaef66fd7ed44fa"
DB_USER="snk_user"
DB_PASSWORD="snk_game_2024"  # New password for game database user
REDIS_PASSWORD="redis_snk_2024"  # Redis password
GAME_ID="101"
ZONE_ID="101"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log_message "${BLUE}========================================${NC}"
log_message "${BLUE}  SNK Game Server aaPanel Deployment   ${NC}"
log_message "${BLUE}========================================${NC}"

# Function to execute command on remote server
remote_exec() {
    local command="$1"
    ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "$command"
}

# Function to copy file to remote server
remote_copy() {
    local local_file="$1"
    local remote_path="$2"
    scp -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$local_file" "$SERVER_USER@$SERVER_IP:$remote_path"
}

# Test server connectivity
log_message "${YELLOW}Testing server connectivity...${NC}"
if ! ping -c 1 "$SERVER_IP" >/dev/null 2>&1; then
    log_message "${RED}ERROR: Cannot reach server $SERVER_IP${NC}"
    exit 1
fi

if ! ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "echo 'SSH connection successful'" >/dev/null 2>&1; then
    log_message "${RED}ERROR: Cannot SSH to server $SERVER_IP${NC}"
    exit 1
fi

log_message "${GREEN}✓ Server connectivity confirmed${NC}"

# Step 1: Process configuration templates locally
log_message "${YELLOW}=== Step 1: Processing Configuration Templates ===${NC}"

# Set environment variables for config processor
export NEW_SERVER_IP="$SERVER_IP"
export DB_PASSWORD="$DB_PASSWORD"
export REDIS_PASSWORD="$REDIS_PASSWORD"
export GAME_ID="$GAME_ID"
export ZONE_ID="$ZONE_ID"

# Run configuration processor
log_message "Running configuration processor..."
if [ -f "./config_processor.sh" ]; then
    ./config_processor.sh
    if [ $? -eq 0 ]; then
        log_message "${GREEN}✓ Configuration processing completed${NC}"
    else
        log_message "${RED}✗ Configuration processing failed${NC}"
        exit 1
    fi
else
    log_message "${RED}ERROR: config_processor.sh not found${NC}"
    exit 1
fi

# Step 2: Setup database on aaPanel server
log_message "${YELLOW}=== Step 2: Database Setup on aaPanel Server ===${NC}"

log_message "Creating game database user and databases..."
remote_exec "mysql -u root -p'$MYSQL_PASSWORD' << 'EOF'
-- Create game database user
CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
CREATE USER IF NOT EXISTS '$DB_USER'@'%' IDENTIFIED BY '$DB_PASSWORD';

-- Create databases
CREATE DATABASE IF NOT EXISTS db_snk CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS db_iap CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS db_zone_1 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS db_zone_101 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Grant permissions
GRANT ALL PRIVILEGES ON db_snk.* TO '$DB_USER'@'localhost';
GRANT ALL PRIVILEGES ON db_snk.* TO '$DB_USER'@'%';
GRANT ALL PRIVILEGES ON db_iap.* TO '$DB_USER'@'localhost';
GRANT ALL PRIVILEGES ON db_iap.* TO '$DB_USER'@'%';
GRANT ALL PRIVILEGES ON db_zone_*.* TO '$DB_USER'@'localhost';
GRANT ALL PRIVILEGES ON db_zone_*.* TO '$DB_USER'@'%';

FLUSH PRIVILEGES;
EOF"

log_message "${GREEN}✓ Database setup completed${NC}"

# Step 3: Import database schemas with IP corrections
log_message "${YELLOW}=== Step 3: Importing Database Schemas ===${NC}"

# Copy database files to server
log_message "Copying database files to server..."
remote_exec "mkdir -p /tmp/snk_db_import"

for db_file in db_*.sql; do
    if [ -f "$db_file" ]; then
        log_message "Copying $db_file..."
        remote_copy "$db_file" "/tmp/snk_db_import/"
    fi
done

# Import databases with IP address corrections
log_message "Importing database schemas..."
remote_exec "
cd /tmp/snk_db_import
for db_file in db_*.sql; do
    if [[ \$db_file == *\"snk\"* ]]; then
        echo \"Importing \$db_file to db_snk...\"
        sed 's/127\\.0\\.0\\.1/$SERVER_IP/g' \$db_file | mysql -u $DB_USER -p'$DB_PASSWORD' db_snk
    elif [[ \$db_file == *\"iap\"* ]]; then
        echo \"Importing \$db_file to db_iap...\"
        sed 's/127\\.0\\.0\\.1/$SERVER_IP/g' \$db_file | mysql -u $DB_USER -p'$DB_PASSWORD' db_iap
    elif [[ \$db_file == *\"zone_1\"* ]]; then
        echo \"Importing \$db_file to db_zone_1...\"
        sed 's/127\\.0\\.0\\.1/$SERVER_IP/g' \$db_file | mysql -u $DB_USER -p'$DB_PASSWORD' db_zone_1
    elif [[ \$db_file == *\"zone_101\"* ]]; then
        echo \"Importing \$db_file to db_zone_101...\"
        sed 's/127\\.0\\.0\\.1/$SERVER_IP/g' \$db_file | mysql -u $DB_USER -p'$DB_PASSWORD' db_zone_101
    fi
done
"

# Apply corrected database migration
log_message "Applying database IP corrections..."
remote_exec "mysql -u $DB_USER -p'$DB_PASSWORD' db_snk << 'EOF'
-- Set variable for new server IP
SET @NEW_SERVER_IP = '$SERVER_IP';

-- Update NON-SFW servers to new IP (preserve SFW services on localhost)
UPDATE t_server 
SET node = @NEW_SERVER_IP 
WHERE node = '127.0.0.1' 
AND server NOT IN ('sfwregistry', 'sfwcontrol');

-- Update game service endpoints (preserve SFW services)
UPDATE t_service 
SET endpoint = REPLACE(endpoint, '127.0.0.1', @NEW_SERVER_IP),
    node = @NEW_SERVER_IP
WHERE server NOT IN ('sfwregistry', 'sfwcontrol') 
AND service NOT LIKE '%Agent%'
AND endpoint LIKE '%127.0.0.1%';

-- Configure external client access
UPDATE t_service 
SET endpoint = REPLACE(endpoint, '127.0.0.1', '0.0.0.0')
WHERE service = 'HandleConn_1' 
AND endpoint LIKE '%18101%';
EOF"

log_message "${GREEN}✓ Database import and migration completed${NC}"

# Step 4: Configure Redis for game server
log_message "${YELLOW}=== Step 4: Redis Configuration ===${NC}"

log_message "Configuring Redis for game server..."
remote_exec "
# Backup original Redis config
cp /etc/redis.conf /etc/redis.conf.backup 2>/dev/null || cp /www/server/redis/redis.conf /www/server/redis/redis.conf.backup 2>/dev/null || true

# Configure Redis (try different possible paths)
REDIS_CONF=''
if [ -f '/etc/redis.conf' ]; then
    REDIS_CONF='/etc/redis.conf'
elif [ -f '/www/server/redis/redis.conf' ]; then
    REDIS_CONF='/www/server/redis/redis.conf'
elif [ -f '/etc/redis/redis.conf' ]; then
    REDIS_CONF='/etc/redis/redis.conf'
fi

if [ -n \"\$REDIS_CONF\" ]; then
    # Set password
    if ! grep -q '^requirepass' \$REDIS_CONF; then
        echo 'requirepass $REDIS_PASSWORD' >> \$REDIS_CONF
    else
        sed -i 's/^requirepass.*/requirepass $REDIS_PASSWORD/' \$REDIS_CONF
    fi
    
    # Allow connections from game server
    sed -i 's/^bind 127.0.0.1/bind 127.0.0.1 $SERVER_IP/' \$REDIS_CONF 2>/dev/null || true
    
    echo 'Redis configuration updated'
else
    echo 'Redis config file not found, will configure later'
fi

# Restart Redis service
systemctl restart redis 2>/dev/null || service redis restart 2>/dev/null || /etc/init.d/redis restart 2>/dev/null || true
"

log_message "${GREEN}✓ Redis configuration completed${NC}"

# Step 5: Create directory structure and deploy files
log_message "${YELLOW}=== Step 5: Directory Setup and File Deployment ===${NC}"

log_message "Creating directory structure..."
remote_exec "
mkdir -p /data/{SNK,sfw,applog/{SNK,sfw,ta_log,auto_restart},backup}
chown -R root:root /data
chmod -R 755 /data
"

# Deploy processed configuration files
log_message "Deploying server binaries and configurations..."
if [ -d "server" ]; then
    log_message "Copying server files..."
    tar -czf server_files.tar.gz server/
    remote_copy "server_files.tar.gz" "/tmp/"
    remote_exec "cd /tmp && tar -xzf server_files.tar.gz && cp -r server/* /data/ && rm -f server_files.tar.gz"
fi

# Deploy management scripts
log_message "Deploying management scripts..."
for script_dir in data/SNK data/sfw; do
    if [ -d "$script_dir" ]; then
        tar -czf "${script_dir##*/}_scripts.tar.gz" "$script_dir"
        remote_copy "${script_dir##*/}_scripts.tar.gz" "/tmp/"
        remote_exec "cd /tmp && tar -xzf ${script_dir##*/}_scripts.tar.gz && cp -r data/${script_dir##*/}/* /data/${script_dir##*/}/ && rm -f ${script_dir##*/}_scripts.tar.gz"
    fi
done

# Deploy system scripts
for script in data/system_*.sh data/game_status_dashboard.sh; do
    if [ -f "$script" ]; then
        remote_copy "$script" "/data/"
    fi
done

# Set proper permissions
log_message "Setting file permissions..."
remote_exec "
find /data -name '*.sh' -exec chmod +x {} \;
chown -R root:root /data
"

log_message "${GREEN}✓ File deployment completed${NC}"

# Step 6: Configure firewall for game server ports
log_message "${YELLOW}=== Step 6: Firewall Configuration ===${NC}"

log_message "Configuring firewall rules..."
remote_exec "
# Game server ports
GAME_PORTS=(2000 2001 2002 7011 7021 7031 7041 7051 7061 7071 7081 7091 7101 7201 8101 8201 8301 17071 17101 18101)

for port in \${GAME_PORTS[@]}; do
    # Try different firewall commands
    ufw allow \$port/tcp 2>/dev/null || \
    firewall-cmd --permanent --add-port=\$port/tcp 2>/dev/null || \
    iptables -A INPUT -p tcp --dport \$port -j ACCEPT 2>/dev/null || \
    true
done

# Reload firewall
ufw reload 2>/dev/null || firewall-cmd --reload 2>/dev/null || service iptables save 2>/dev/null || true
"

log_message "${GREEN}✓ Firewall configuration completed${NC}"

# Step 7: Final validation
log_message "${YELLOW}=== Step 7: Deployment Validation ===${NC}"

log_message "Validating deployment..."
remote_exec "
echo 'Checking directory structure:'
ls -la /data/

echo 'Checking database connectivity:'
mysql -u $DB_USER -p'$DB_PASSWORD' -e 'SHOW DATABASES;' | grep -E '(db_snk|db_iap|db_zone)'

echo 'Checking Redis connectivity:'
redis-cli -a '$REDIS_PASSWORD' ping 2>/dev/null || echo 'Redis may need manual configuration'

echo 'Checking script permissions:'
find /data -name '*.sh' -exec ls -la {} \; | head -5
"

# Generate deployment summary
log_message "${BLUE}========================================${NC}"
log_message "${GREEN}    Deployment Completed Successfully   ${NC}"
log_message "${BLUE}========================================${NC}"

log_message "${BLUE}Deployment Summary:${NC}"
log_message "• Server: $SERVER_IP (aaPanel)"
log_message "• Database User: $DB_USER"
log_message "• Game ID: $GAME_ID"
log_message "• Zone ID: $ZONE_ID"
log_message "• Configuration: Processed and deployed"

log_message "${BLUE}Next Steps:${NC}"
log_message "1. Start SFW Framework: ssh $SERVER_USER@$SERVER_IP 'cd /data/sfw && ./start_manual.sh'"
log_message "2. Start Game Servers: ssh $SERVER_USER@$SERVER_IP 'cd /data/SNK && ./start_manual.sh'"
log_message "3. Check Status: ssh $SERVER_USER@$SERVER_IP '/data/game_status_dashboard.sh'"
log_message "4. Monitor Logs: ssh $SERVER_USER@$SERVER_IP 'tail -f /data/applog/SNK/*'"

log_message "${GREEN}✅ aaPanel deployment completed successfully!${NC}"

# Clean up local temporary files
rm -f server_files.tar.gz SNK_scripts.tar.gz sfw_scripts.tar.gz 2>/dev/null || true
