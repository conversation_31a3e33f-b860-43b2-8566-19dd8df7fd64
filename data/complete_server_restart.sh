#!/bin/bash

# Complete Server Restart Cycle Script
# Author: Auto-generated for SNK Game Management
# Description: Performs complete server restart with verification and error handling

# Configuration
SERVER_IP="**************"
SERVER_USER="root"
LOG_FILE="/tmp/server_restart_$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to execute command on remote server
remote_exec() {
    local command="$1"
    local description="$2"
    
    log_message "${BLUE}Executing: $description${NC}"
    log_message "Command: $command"
    
    if ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "$command" 2>&1 | tee -a "$LOG_FILE"; then
        log_message "${GREEN}✓ Success: $description${NC}"
        return 0
    else
        log_message "${RED}✗ Failed: $description${NC}"
        return 1
    fi
}

# Function to check server connectivity
check_connectivity() {
    log_message "${YELLOW}Checking server connectivity...${NC}"
    
    if ping -c 3 "$SERVER_IP" >/dev/null 2>&1; then
        log_message "${GREEN}✓ Server is reachable${NC}"
    else
        log_message "${RED}✗ Server is not reachable${NC}"
        return 1
    fi
    
    if ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "echo 'SSH connection test'" >/dev/null 2>&1; then
        log_message "${GREEN}✓ SSH connection successful${NC}"
        return 0
    else
        log_message "${RED}✗ SSH connection failed${NC}"
        return 1
    fi
}

log_message "${BLUE}========================================${NC}"
log_message "${BLUE}    Complete Server Restart Cycle      ${NC}"
log_message "${BLUE}========================================${NC}"
log_message "Target Server: $SERVER_IP"
log_message "Log File: $LOG_FILE"

# Check connectivity first
if ! check_connectivity; then
    log_message "${RED}Cannot proceed without server connectivity${NC}"
    exit 1
fi

# Step 1: Stop the server system safely
log_message "${YELLOW}=== STEP 1: STOPPING SERVER SYSTEM SAFELY ===${NC}"

# Check if system_stop.sh exists
remote_exec "[ -f /data/system_stop.sh ] && echo 'system_stop.sh found' || echo 'system_stop.sh not found'" "Check system_stop.sh existence"

# Execute system stop
remote_exec "cd /data && sudo ./system_stop.sh" "Execute system shutdown"

# Verify all processes stopped
log_message "${YELLOW}Verifying all processes have stopped...${NC}"
remote_exec "ps aux | grep -E '(Server|server|sfw|SFW)' | grep -v grep | wc -l" "Count remaining server processes"

# Check critical ports are free
remote_exec "netstat -tlnp | grep -E '(2000|2001|2002|8101|8201|8301)' || echo 'No critical ports in use'" "Check critical ports status"

# Step 2: Clean all log files
log_message "${YELLOW}=== STEP 2: CLEANING ALL LOG FILES ===${NC}"

# Preserve directory structure but clear log files
remote_exec "find /data/applog -name '*.log' -type f -delete 2>/dev/null || echo 'Log cleanup completed'" "Remove all .log files"

# Recreate necessary directories
remote_exec "mkdir -p /data/applog/{SNK,sfw,ta_log,auto_restart}" "Recreate log directories"

# Verify log cleanup
remote_exec "find /data/applog -name '*.log' -type f | wc -l" "Verify log files removed"

# Step 3: Restart the server system
log_message "${YELLOW}=== STEP 3: RESTARTING SERVER SYSTEM ===${NC}"

# Check if system_start.sh exists
remote_exec "[ -f /data/system_start.sh ] && echo 'system_start.sh found' || echo 'system_start.sh not found'" "Check system_start.sh existence"

# Execute system start
remote_exec "cd /data && sudo ./system_start.sh" "Execute system startup"

# Wait for services to fully start
log_message "${YELLOW}Waiting for services to fully initialize...${NC}"
sleep 30

# Step 4: Verify successful startup
log_message "${YELLOW}=== STEP 4: VERIFYING SUCCESSFUL STARTUP ===${NC}"

# Check system dependencies
log_message "${BLUE}Checking system dependencies:${NC}"
remote_exec "systemctl is-active mysql || echo 'MySQL not active'" "Check MySQL status"
remote_exec "systemctl is-active redis || echo 'Redis not active'" "Check Redis status"

# Check critical ports
log_message "${BLUE}Checking critical service ports:${NC}"
remote_exec "netstat -tlnp | grep ':3306 ' && echo 'MySQL port OK' || echo 'MySQL port not listening'" "Check MySQL port 3306"
remote_exec "netstat -tlnp | grep ':6379 ' && echo 'Redis port OK' || echo 'Redis port not listening'" "Check Redis port 6379"
remote_exec "netstat -tlnp | grep ':2000 ' && echo 'SFW Registry OK' || echo 'SFW Registry not listening'" "Check SFW Registry port 2000"
remote_exec "netstat -tlnp | grep ':2001 ' && echo 'SFW Control OK' || echo 'SFW Control not listening'" "Check SFW Control port 2001"
remote_exec "netstat -tlnp | grep ':2002 ' && echo 'SFW Agent OK' || echo 'SFW Agent not listening'" "Check SFW Agent port 2002"
remote_exec "netstat -tlnp | grep ':8101 ' && echo 'GateServer OK' || echo 'GateServer not listening'" "Check GateServer port 8101"
remote_exec "netstat -tlnp | grep ':8201 ' && echo 'GameServer OK' || echo 'GameServer not listening'" "Check GameServer port 8201"
remote_exec "netstat -tlnp | grep ':8301 ' && echo 'BattleServer OK' || echo 'BattleServer not listening'" "Check BattleServer port 8301"

# Check running processes
log_message "${BLUE}Checking running processes:${NC}"
remote_exec "ps aux | grep -E '(Server|server)' | grep -v grep | wc -l" "Count game server processes"
remote_exec "ps aux | grep -E '(sfw|SFW)' | grep -v grep | wc -l" "Count SFW framework processes"

# Run comprehensive status dashboard
log_message "${BLUE}Running comprehensive status check:${NC}"
remote_exec "[ -f /data/game_status_dashboard.sh ] && /data/game_status_dashboard.sh || echo 'Status dashboard not available'" "Execute status dashboard"

# Step 5: Error handling and documentation
log_message "${YELLOW}=== STEP 5: ERROR HANDLING AND DOCUMENTATION ===${NC}"

# Check for errors in startup logs
remote_exec "find /data/applog -name '*.log' -type f -exec grep -l -i error {} \; 2>/dev/null || echo 'No error logs found'" "Check for error logs"

# Generate final status report
log_message "${BLUE}Generating final status report...${NC}"
remote_exec "
echo '=== FINAL SYSTEM STATUS REPORT ==='
echo 'Date: $(date)'
echo 'Server: $(hostname)'
echo ''
echo '=== System Services ==='
systemctl is-active mysql && echo 'MySQL: ACTIVE' || echo 'MySQL: INACTIVE'
systemctl is-active redis && echo 'Redis: ACTIVE' || echo 'Redis: INACTIVE'
echo ''
echo '=== Critical Ports ==='
netstat -tlnp | grep -E '(3306|6379|2000|2001|2002|8101|8201|8301)' | awk '{print \$4}' | sort
echo ''
echo '=== Process Summary ==='
echo \"Game Server Processes: \$(ps aux | grep -E '(Server|server)' | grep -v grep | wc -l)\"
echo \"SFW Framework Processes: \$(ps aux | grep -E '(sfw|SFW)' | grep -v grep | wc -l)\"
echo ''
echo '=== Log Files Created ==='
find /data/applog -name '*.log' -type f | wc -l
echo ''
echo '=== Disk Usage ==='
df -h /data
" "Generate final status report"

# Summary
log_message "${BLUE}========================================${NC}"
log_message "${BLUE}        RESTART CYCLE SUMMARY           ${NC}"
log_message "${BLUE}========================================${NC}"

log_message "Server restart cycle completed for $SERVER_IP"
log_message "Detailed log available at: $LOG_FILE"
log_message ""
log_message "${GREEN}Next steps:${NC}"
log_message "1. Review the final status report above"
log_message "2. Check log files in /data/applog/ for any errors"
log_message "3. Monitor system performance for stability"
log_message "4. Document any issues found and fixes applied"

log_message "${BLUE}========================================${NC}"
