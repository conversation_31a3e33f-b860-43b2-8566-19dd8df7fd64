#!/bin/bash

# Manual Server Restart Steps
# Execute these commands step by step on server 154.205.143.81

echo "=========================================="
echo "    MANUAL SERVER RESTART PROCEDURE"
echo "=========================================="
echo ""

echo "STEP 1: CONNECT TO SERVER"
echo "ssh root@154.205.143.81"
echo ""

echo "STEP 2: STOP SERVER SYSTEM SAFELY"
echo "# Check current system status"
echo "ps aux | grep -E '(Server|server|sfw)' | grep -v grep"
echo "netstat -tlnp | grep -E '(2000|2001|2002|8101|8201|8301)'"
echo ""
echo "# Execute system shutdown"
echo "cd /data"
echo "sudo ./system_stop.sh"
echo ""
echo "# Verify all processes stopped"
echo "ps aux | grep -E '(Server|server|sfw)' | grep -v grep"
echo "netstat -tlnp | grep -E '(2000|2001|2002|8101|8201|8301)'"
echo ""

echo "STEP 3: CLEAN ALL LOG FILES"
echo "# Remove all log files but preserve directory structure"
echo "find /data/applog -name '*.log' -type f -delete"
echo ""
echo "# Recreate necessary directories"
echo "mkdir -p /data/applog/{SNK,sfw,ta_log,auto_restart}"
echo ""
echo "# Verify cleanup"
echo "find /data/applog -name '*.log' -type f | wc -l"
echo "ls -la /data/applog/"
echo ""

echo "STEP 4: RESTART SERVER SYSTEM"
echo "# Execute system startup"
echo "cd /data"
echo "sudo ./system_start.sh"
echo ""
echo "# Wait for services to initialize"
echo "sleep 30"
echo ""

echo "STEP 5: VERIFY SUCCESSFUL STARTUP"
echo "# Check system dependencies"
echo "systemctl status mysql redis"
echo ""
echo "# Check critical ports"
echo "netstat -tlnp | grep -E '(3306|6379|2000|2001|2002|8101|8201|8301)'"
echo ""
echo "# Check running processes"
echo "ps aux | grep -E '(Server|server)' | grep -v grep"
echo "ps aux | grep -E '(sfw|SFW)' | grep -v grep"
echo ""
echo "# Run status dashboard"
echo "/data/game_status_dashboard.sh"
echo ""

echo "STEP 6: ERROR HANDLING"
echo "# Check for errors in logs"
echo "find /data/applog -name '*.log' -type f -exec grep -i error {} +"
echo ""
echo "# Check specific service logs"
echo "tail -50 /data/applog/system_startup.log"
echo "tail -50 /data/applog/SNK/*/SNK.*.log"
echo ""

echo "STEP 7: FINAL VERIFICATION"
echo "# Generate comprehensive status report"
echo "echo '=== FINAL STATUS REPORT ==='"
echo "date"
echo "hostname"
echo "systemctl is-active mysql redis"
echo "netstat -tlnp | grep -E '(3306|6379|2000|2001|2002|8101|8201|8301)' | wc -l"
echo "ps aux | grep -E '(Server|server)' | grep -v grep | wc -l"
echo "ps aux | grep -E '(sfw|SFW)' | grep -v grep | wc -l"
echo "df -h /data"
echo ""

echo "=========================================="
echo "    TROUBLESHOOTING COMMANDS"
echo "=========================================="
echo ""
echo "IF MYSQL NOT RUNNING:"
echo "systemctl start mysql"
echo "systemctl status mysql"
echo ""
echo "IF REDIS NOT RUNNING:"
echo "systemctl start redis"
echo "systemctl status redis"
echo ""
echo "IF SFW FRAMEWORK ISSUES:"
echo "cd /data/sfw"
echo "./stop_manual.sh"
echo "./start_manual.sh"
echo ""
echo "IF GAME SERVERS NOT STARTING:"
echo "cd /data/SNK"
echo "./stop_manual.sh"
echo "./start_manual.sh"
echo ""
echo "FORCE CLEANUP IF NEEDED:"
echo "pkill -f 'Server|sfw'"
echo "sleep 5"
echo "ps aux | grep -E '(Server|sfw)' | grep -v grep"
echo ""
echo "CHECK LOG FILES FOR ERRORS:"
echo "tail -100 /data/applog/system_startup.log"
echo "grep -i error /data/applog/system_startup.log"
echo ""

echo "=========================================="
