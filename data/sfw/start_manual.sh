#!/bin/bash

# SFW Framework Manual Start Script
# Author: Auto-generated for SNK Game Management
# Description: Manually starts SFW Framework services with proper dependency checking

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/data/applog/sfw_startup_manual.log"
MAX_WAIT_TIME=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
set -e
trap 'log_message "${RED}ERROR: Script terminated unexpectedly at line $LINENO${NC}"' ERR

# Create log directory
mkdir -p "/data/applog"

log_message "${BLUE}=== SFW Framework Manual Startup ===${NC}"

# Function to check if a port is listening
check_port() {
    local port=$1
    local service_name=$2
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        log_message "${GREEN}✓ $service_name (port $port) is running${NC}"
        return 0
    else
        log_message "${RED}✗ $service_name (port $port) is not running${NC}"
        return 1
    fi
}

# Function to wait for service to start
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_wait=$3
    local count=0
    
    while [ $count -lt $max_wait ]; do
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            log_message "${GREEN}✓ $service_name started successfully${NC}"
            return 0
        fi
        sleep 1
        ((count++))
    done
    log_message "${RED}✗ $service_name failed to start within $max_wait seconds${NC}"
    return 1
}

# Check system dependencies
log_message "${YELLOW}=== Checking System Dependencies ===${NC}"

# Check MySQL
if ! systemctl is-active --quiet mysql 2>/dev/null; then
    log_message "${YELLOW}Starting MySQL service...${NC}"
    systemctl start mysql || {
        log_message "${RED}Failed to start MySQL service${NC}"
        exit 1
    }
    sleep 5
fi

# Clear old SFW logs
log_message "${YELLOW}=== Clearing Old SFW Logs ===${NC}"
rm -rf /data/applog/sfw/*

# Start SFW services in proper order
log_message "${YELLOW}=== Starting SFW Framework Services ===${NC}"

# 1. Start sfwregistry (Service Registry)
log_message "Starting sfwregistry (Service Registry)..."
if [ -d "$SCRIPT_DIR/sfwregistry" ] && [ -f "$SCRIPT_DIR/sfwregistry/start.sh" ]; then
    cd "$SCRIPT_DIR/sfwregistry"
    ./start.sh
    
    if wait_for_service 2000 "sfwregistry" 15; then
        log_message "${GREEN}✓ sfwregistry started successfully${NC}"
    else
        log_message "${RED}✗ sfwregistry failed to start${NC}"
        exit 1
    fi
else
    log_message "${RED}✗ sfwregistry directory or start script not found${NC}"
    exit 1
fi

sleep 3

# 2. Start sfwcontrol (Service Control)
log_message "Starting sfwcontrol (Service Control)..."
if [ -d "$SCRIPT_DIR/sfwcontrol" ] && [ -f "$SCRIPT_DIR/sfwcontrol/start.sh" ]; then
    cd "$SCRIPT_DIR/sfwcontrol"
    ./start.sh
    
    if wait_for_service 2001 "sfwcontrol" 15; then
        log_message "${GREEN}✓ sfwcontrol started successfully${NC}"
    else
        log_message "${RED}✗ sfwcontrol failed to start${NC}"
        exit 1
    fi
else
    log_message "${RED}✗ sfwcontrol directory or start script not found${NC}"
    exit 1
fi

sleep 3

# 3. Start sfwagent (Service Agent)
log_message "Starting sfwagent (Service Agent)..."
if [ -d "$SCRIPT_DIR/sfwagent" ] && [ -f "$SCRIPT_DIR/sfwagent/start.sh" ]; then
    cd "$SCRIPT_DIR/sfwagent"
    ./start.sh
    
    if wait_for_service 2002 "sfwagent" 15; then
        log_message "${GREEN}✓ sfwagent started successfully${NC}"
    else
        log_message "${RED}✗ sfwagent failed to start${NC}"
        exit 1
    fi
else
    log_message "${RED}✗ sfwagent directory or start script not found${NC}"
    exit 1
fi

# Final verification
log_message "${YELLOW}=== Final SFW Framework Status Check ===${NC}"

sfw_services=("2000:sfwregistry" "2001:sfwcontrol" "2002:sfwagent")
failed_services=0

for service_port in "${sfw_services[@]}"; do
    port=$(echo "$service_port" | cut -d: -f1)
    service=$(echo "$service_port" | cut -d: -f2)
    
    if ! check_port "$port" "$service"; then
        ((failed_services++))
    fi
done

# Summary
log_message "${BLUE}=== SFW Framework Startup Summary ===${NC}"
if [ $failed_services -eq 0 ]; then
    log_message "${GREEN}✅ All SFW Framework services started successfully!${NC}"
    log_message "SFW Framework is ready for game server operations."
else
    log_message "${RED}✗ $failed_services SFW services failed to start${NC}"
    log_message "Please check individual service logs in /data/applog/sfw/"
    exit 1
fi

# Show running SFW processes
log_message "${BLUE}=== Running SFW Processes ===${NC}"
ps aux | grep -E "(sfw|SFW)" | grep -v grep | while read line; do
    log_message "$line"
done

log_message "${BLUE}=== SFW Framework Manual Startup Complete ===${NC}"
log_message "Use './stop_manual.sh' to stop SFW Framework"
log_message "SFW Framework is now ready for game server startup"
