#!/bin/bash

# SFW Framework Manual Stop Script
# Author: Auto-generated for SNK Game Management
# Description: Manually stops SFW Framework services with proper shutdown procedures

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/data/applog/sfw_shutdown_manual.log"
MAX_WAIT_TIME=15

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
set -e
trap 'log_message "${RED}ERROR: Script terminated unexpectedly at line $LINENO${NC}"' ERR

log_message "${BLUE}=== SFW Framework Manual Shutdown ===${NC}"

# Function to wait for process to stop
wait_for_process_stop() {
    local process_name=$1
    local max_wait=$2
    local count=0
    
    while [ $count -lt $max_wait ]; do
        if ! pgrep -f "$process_name" > /dev/null; then
            return 0
        fi
        sleep 1
        ((count++))
    done
    return 1
}

# Function to gracefully stop a service
stop_sfw_service() {
    local service_dir=$1
    local service_name=$2
    
    if [ -d "$service_dir" ] && [ -f "$service_dir/stop.sh" ]; then
        log_message "Stopping $service_name..."
        cd "$service_dir"
        ./stop.sh
        
        # Wait for process to stop
        if wait_for_process_stop "$service_name" 10; then
            log_message "${GREEN}✓ $service_name stopped successfully${NC}"
        else
            log_message "${YELLOW}⚠ $service_name did not stop gracefully, forcing...${NC}"
            pkill -f "$service_name" 2>/dev/null || true
            sleep 2
            
            # Final check
            if wait_for_process_stop "$service_name" 5; then
                log_message "${GREEN}✓ $service_name force stopped${NC}"
            else
                log_message "${RED}✗ $service_name still running after force kill${NC}"
            fi
        fi
    else
        log_message "${YELLOW}⚠ $service_name stop script not found${NC}"
    fi
}

# Stop SFW services in reverse order (agent -> control -> registry)
log_message "${YELLOW}=== Stopping SFW Framework Services ===${NC}"

# 1. Stop sfwagent (Service Agent)
stop_sfw_service "$SCRIPT_DIR/sfwagent" "sfwagent"

# 2. Stop sfwcontrol (Service Control)
stop_sfw_service "$SCRIPT_DIR/sfwcontrol" "sfwcontrol"

# 3. Stop sfwregistry (Service Registry)
stop_sfw_service "$SCRIPT_DIR/sfwregistry" "sfwregistry"

# Force cleanup any remaining SFW processes
log_message "${YELLOW}=== Cleaning Up Remaining SFW Processes ===${NC}"

sfw_processes=("sfwagent" "sfwcontrol" "sfwregistry")

for process in "${sfw_processes[@]}"; do
    if pgrep -f "$process" > /dev/null; then
        log_message "${YELLOW}Found remaining $process processes, cleaning up...${NC}"
        pkill -f "$process" 2>/dev/null || true
        sleep 1
        
        # Use SIGKILL if still running
        if pgrep -f "$process" > /dev/null; then
            log_message "${YELLOW}Using SIGKILL for $process...${NC}"
            pkill -9 -f "$process" 2>/dev/null || true
            sleep 1
        fi
    fi
done

# Final verification
log_message "${YELLOW}=== Final SFW Framework Status Check ===${NC}"

# Check if SFW ports are still in use
sfw_ports=("2000" "2001" "2002")
ports_in_use=0

for port in "${sfw_ports[@]}"; do
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        log_message "${YELLOW}⚠ Port $port still in use${NC}"
        ((ports_in_use++))
    else
        log_message "${GREEN}✓ Port $port is free${NC}"
    fi
done

# Check for remaining SFW processes
remaining_processes=$(ps aux | grep -E "(sfw|SFW)" | grep -v grep | grep -v "stop_manual.sh" | wc -l)

# Summary
log_message "${BLUE}=== SFW Framework Shutdown Summary ===${NC}"
if [ "$remaining_processes" -eq 0 ] && [ "$ports_in_use" -eq 0 ]; then
    log_message "${GREEN}✅ All SFW Framework services stopped successfully!${NC}"
    log_message "SFW Framework is completely shut down."
elif [ "$remaining_processes" -eq 0 ] && [ "$ports_in_use" -gt 0 ]; then
    log_message "${YELLOW}⚠ SFW processes stopped but some ports still in use${NC}"
    log_message "Ports may be in TIME_WAIT state and will be freed shortly."
else
    log_message "${YELLOW}⚠ $remaining_processes SFW processes may still be running:${NC}"
    ps aux | grep -E "(sfw|SFW)" | grep -v grep | grep -v "stop_manual.sh" | while read line; do
        log_message "  $line"
    done
fi

log_message "${BLUE}=== SFW Framework Manual Shutdown Complete ===${NC}"
log_message "Use './start_manual.sh' to start SFW Framework"
log_message "SFW Framework shutdown completed"
