#!/bin/bash

# Automated Deployment Script for SNK Game Server
# Author: Auto-generated for SNK Game Management
# Description: Automatically deploys all scripts to the remote server

# Configuration
SERVER_IP="**************"
SERVER_USER="root"
LOCAL_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_DIR="/data/backup/$(date +%Y%m%d_%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log_message "${BLUE}========================================${NC}"
log_message "${BLUE}    SNK Game Server Deployment Tool    ${NC}"
log_message "${BLUE}========================================${NC}"

# Function to execute command on remote server
remote_exec() {
    local command="$1"
    ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "$command"
}

# Function to copy file to remote server
remote_copy() {
    local local_file="$1"
    local remote_path="$2"
    scp -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$local_file" "$SERVER_USER@$SERVER_IP:$remote_path"
}

# Test server connectivity
log_message "${YELLOW}Testing server connectivity...${NC}"
if ! ping -c 1 "$SERVER_IP" >/dev/null 2>&1; then
    log_message "${RED}ERROR: Cannot reach server $SERVER_IP${NC}"
    exit 1
fi

if ! ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "echo 'Connection test successful'" >/dev/null 2>&1; then
    log_message "${RED}ERROR: Cannot SSH to server $SERVER_IP${NC}"
    exit 1
fi

log_message "${GREEN}✓ Server connectivity confirmed${NC}"

# Create backup directory on server
log_message "${YELLOW}Creating backup directory on server...${NC}"
remote_exec "mkdir -p $BACKUP_DIR"

# Backup existing scripts
log_message "${YELLOW}Backing up existing scripts...${NC}"
remote_exec "
    cp /data/SNK/start.sh $BACKUP_DIR/start_snk_old.sh 2>/dev/null || echo 'No existing SNK start.sh'
    cp /data/SNK/stop.sh $BACKUP_DIR/stop_snk_old.sh 2>/dev/null || echo 'No existing SNK stop.sh'
    cp /data/sfw/start.sh $BACKUP_DIR/start_sfw_old.sh 2>/dev/null || echo 'No existing SFW start.sh'
    cp /data/sfw/stop.sh $BACKUP_DIR/stop_sfw_old.sh 2>/dev/null || echo 'No existing SFW stop.sh'
    echo 'Backup completed in $BACKUP_DIR'
"

# Create required directories
log_message "${YELLOW}Creating required directories...${NC}"
remote_exec "
    mkdir -p /data/applog/{SNK,sfw,ta_log,auto_restart}
    chown -R root:root /data/applog
    chmod -R 755 /data/applog
"

# Deploy SNK scripts
log_message "${YELLOW}Deploying SNK game server scripts...${NC}"
remote_copy "$LOCAL_DIR/SNK/start_manual.sh" "/data/SNK/start_manual.sh"
remote_copy "$LOCAL_DIR/SNK/stop_manual.sh" "/data/SNK/stop_manual.sh"
remote_copy "$LOCAL_DIR/SNK/start.sh" "/data/SNK/start.sh"
remote_copy "$LOCAL_DIR/SNK/stop.sh" "/data/SNK/stop.sh"

# Deploy SFW scripts
log_message "${YELLOW}Deploying SFW framework scripts...${NC}"
remote_copy "$LOCAL_DIR/sfw/start_manual.sh" "/data/sfw/start_manual.sh"
remote_copy "$LOCAL_DIR/sfw/stop_manual.sh" "/data/sfw/stop_manual.sh"

# Deploy system-wide scripts
log_message "${YELLOW}Deploying system-wide scripts...${NC}"
remote_copy "$LOCAL_DIR/system_start.sh" "/data/system_start.sh"
remote_copy "$LOCAL_DIR/system_stop.sh" "/data/system_stop.sh"

# Deploy documentation
log_message "${YELLOW}Deploying documentation...${NC}"
remote_copy "$LOCAL_DIR/SERVER_MANAGEMENT_GUIDE.md" "/data/SERVER_MANAGEMENT_GUIDE.md"
remote_copy "$LOCAL_DIR/DEPLOYMENT_INSTRUCTIONS.md" "/data/DEPLOYMENT_INSTRUCTIONS.md"

# Set proper permissions
log_message "${YELLOW}Setting script permissions...${NC}"
remote_exec "
    chmod +x /data/SNK/*.sh
    chmod +x /data/sfw/*.sh
    chmod +x /data/system_*.sh
    chown root:root /data/SNK/*.sh /data/sfw/*.sh /data/system_*.sh
"

# Verify deployment
log_message "${YELLOW}Verifying deployment...${NC}"
remote_exec "
    echo 'Checking script permissions:'
    ls -la /data/SNK/*.sh
    ls -la /data/sfw/*.sh
    ls -la /data/system_*.sh
    
    echo 'Verifying script syntax:'
    bash -n /data/SNK/start_manual.sh && echo 'SNK start_manual.sh: OK'
    bash -n /data/SNK/stop_manual.sh && echo 'SNK stop_manual.sh: OK'
    bash -n /data/sfw/start_manual.sh && echo 'SFW start_manual.sh: OK'
    bash -n /data/sfw/stop_manual.sh && echo 'SFW stop_manual.sh: OK'
    bash -n /data/system_start.sh && echo 'system_start.sh: OK'
    bash -n /data/system_stop.sh && echo 'system_stop.sh: OK'
"

# Test basic functionality
log_message "${YELLOW}Testing basic functionality...${NC}"
remote_exec "
    echo 'Testing SFW Framework management:'
    cd /data/sfw
    ./stop_manual.sh >/dev/null 2>&1 || echo 'SFW stop completed'
    sleep 3
    ./start_manual.sh
    
    echo 'Checking SFW services:'
    netstat -tlnp | grep -E '(2000|2001|2002)' || echo 'SFW services may need time to start'
"

# Final status
log_message "${GREEN}========================================${NC}"
log_message "${GREEN}    Deployment Completed Successfully   ${NC}"
log_message "${GREEN}========================================${NC}"

log_message "${BLUE}Deployment Summary:${NC}"
log_message "• Server: $SERVER_IP"
log_message "• Backup Location: $BACKUP_DIR"
log_message "• Scripts Deployed: SNK, SFW, System-wide"
log_message "• Documentation: Available in /data/"

log_message "${BLUE}Next Steps:${NC}"
log_message "1. Test game server startup: ssh $SERVER_USER@$SERVER_IP 'cd /data/SNK && ./start_manual.sh'"
log_message "2. Check system status: ssh $SERVER_USER@$SERVER_IP '/data/game_status_dashboard.sh'"
log_message "3. Review documentation: /data/SERVER_MANAGEMENT_GUIDE.md"

log_message "${GREEN}✅ Deployment completed successfully!${NC}"
