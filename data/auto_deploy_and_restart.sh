#!/bin/bash

# Auto Deploy and Restart Script
# Tự động upload code lên SSH và khởi động hệ thống

# Configuration
SERVER_IP="**************"
SERVER_USER="root"
LOCAL_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="/tmp/auto_deploy_restart_${TIMESTAMP}.log"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log_message "${BLUE}========================================${NC}"
log_message "${BLUE}    AUTO DEPLOY AND RESTART SYSTEM     ${NC}"
log_message "${BLUE}========================================${NC}"

# Step 1: Test connectivity
log_message "${YELLOW}=== STEP 1: TESTING SERVER CONNECTIVITY ===${NC}"
if ping -c 2 "$SERVER_IP" >/dev/null 2>&1; then
    log_message "${GREEN}✓ Server ping successful${NC}"
else
    log_message "${RED}✗ Cannot ping server${NC}"
    exit 1
fi

if ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "echo 'SSH test OK'" >/dev/null 2>&1; then
    log_message "${GREEN}✓ SSH connection successful${NC}"
else
    log_message "${RED}✗ SSH connection failed${NC}"
    exit 1
fi

# Step 2: Create backup on server
log_message "${YELLOW}=== STEP 2: CREATING BACKUP ON SERVER ===${NC}"
ssh "$SERVER_USER@$SERVER_IP" "
    mkdir -p /data/backup/${TIMESTAMP}
    cp /data/SNK/start.sh /data/backup/${TIMESTAMP}/start_snk_old.sh 2>/dev/null || echo 'No old SNK start.sh'
    cp /data/SNK/stop.sh /data/backup/${TIMESTAMP}/stop_snk_old.sh 2>/dev/null || echo 'No old SNK stop.sh'
    cp /data/sfw/start.sh /data/backup/${TIMESTAMP}/start_sfw_old.sh 2>/dev/null || echo 'No old SFW start.sh'
    cp /data/sfw/stop.sh /data/backup/${TIMESTAMP}/stop_sfw_old.sh 2>/dev/null || echo 'No old SFW stop.sh'
    echo 'Backup completed in /data/backup/${TIMESTAMP}'
"

# Step 3: Upload all scripts
log_message "${YELLOW}=== STEP 3: UPLOADING SCRIPTS TO SERVER ===${NC}"

# Upload SNK scripts
log_message "Uploading SNK scripts..."
scp "$LOCAL_DIR/SNK/start_manual.sh" "$SERVER_USER@$SERVER_IP:/data/SNK/"
scp "$LOCAL_DIR/SNK/stop_manual.sh" "$SERVER_USER@$SERVER_IP:/data/SNK/"
scp "$LOCAL_DIR/SNK/start.sh" "$SERVER_USER@$SERVER_IP:/data/SNK/"
scp "$LOCAL_DIR/SNK/stop.sh" "$SERVER_USER@$SERVER_IP:/data/SNK/"

# Upload SFW scripts
log_message "Uploading SFW scripts..."
scp "$LOCAL_DIR/sfw/start_manual.sh" "$SERVER_USER@$SERVER_IP:/data/sfw/"
scp "$LOCAL_DIR/sfw/stop_manual.sh" "$SERVER_USER@$SERVER_IP:/data/sfw/"

# Upload system scripts
log_message "Uploading system scripts..."
scp "$LOCAL_DIR/system_start.sh" "$SERVER_USER@$SERVER_IP:/data/"
scp "$LOCAL_DIR/system_stop.sh" "$SERVER_USER@$SERVER_IP:/data/"

# Upload documentation
log_message "Uploading documentation..."
scp "$LOCAL_DIR/SERVER_MANAGEMENT_GUIDE.md" "$SERVER_USER@$SERVER_IP:/data/"

log_message "${GREEN}✓ All scripts uploaded successfully${NC}"

# Step 4: Set permissions
log_message "${YELLOW}=== STEP 4: SETTING PERMISSIONS ===${NC}"
ssh "$SERVER_USER@$SERVER_IP" "
    chmod +x /data/SNK/*.sh
    chmod +x /data/sfw/*.sh
    chmod +x /data/system_*.sh
    chown root:root /data/SNK/*.sh /data/sfw/*.sh /data/system_*.sh
    mkdir -p /data/applog/{SNK,sfw,ta_log,auto_restart}
    chown -R root:root /data/applog
    chmod -R 755 /data/applog
"
log_message "${GREEN}✓ Permissions set successfully${NC}"

# Step 5: Execute complete restart cycle
log_message "${YELLOW}=== STEP 5: EXECUTING COMPLETE RESTART CYCLE ===${NC}"

# Stop system safely
log_message "${BLUE}Stopping system safely...${NC}"
ssh "$SERVER_USER@$SERVER_IP" "
    echo '=== STOPPING SYSTEM ==='
    cd /data
    if [ -f system_stop.sh ]; then
        ./system_stop.sh
    else
        echo 'Using manual stop procedures'
        cd /data/SNK && ./stop_manual.sh 2>/dev/null || echo 'SNK stop completed'
        cd /data/sfw && ./stop_manual.sh 2>/dev/null || echo 'SFW stop completed'
        pkill -f 'Server|sfw' 2>/dev/null || echo 'Force kill completed'
    fi
    
    echo '=== VERIFYING STOP ==='
    echo \"Remaining processes: \$(ps aux | grep -E '(Server|sfw)' | grep -v grep | wc -l)\"
    echo \"Ports in use: \$(netstat -tlnp | grep -E '(2000|2001|2002|8101|8201|8301)' | wc -l)\"
"

# Clean logs
log_message "${BLUE}Cleaning log files...${NC}"
ssh "$SERVER_USER@$SERVER_IP" "
    echo '=== CLEANING LOGS ==='
    find /data/applog -name '*.log' -type f -delete 2>/dev/null || echo 'Log cleanup completed'
    mkdir -p /data/applog/{SNK,sfw,ta_log,auto_restart}
    echo \"Log files remaining: \$(find /data/applog -name '*.log' -type f | wc -l)\"
"

# Start system
log_message "${BLUE}Starting system...${NC}"
ssh "$SERVER_USER@$SERVER_IP" "
    echo '=== STARTING SYSTEM ==='
    cd /data
    if [ -f system_start.sh ]; then
        ./system_start.sh
    else
        echo 'Using manual start procedures'
        # Start dependencies
        systemctl start mysql redis 2>/dev/null || echo 'Dependencies start attempted'
        sleep 5
        
        # Start SFW
        cd /data/sfw && ./start_manual.sh
        sleep 10
        
        # Start game servers
        cd /data/SNK && ./start_manual.sh
        sleep 15
    fi
"

# Step 6: Verify system status
log_message "${YELLOW}=== STEP 6: VERIFYING SYSTEM STATUS ===${NC}"
ssh "$SERVER_USER@$SERVER_IP" "
    echo '=== FINAL SYSTEM STATUS ==='
    echo 'Date: \$(date)'
    echo 'Server: \$(hostname)'
    echo ''
    
    echo '=== System Services ==='
    systemctl is-active mysql && echo 'MySQL: ACTIVE' || echo 'MySQL: INACTIVE'
    systemctl is-active redis && echo 'Redis: ACTIVE' || echo 'Redis: INACTIVE'
    echo ''
    
    echo '=== Critical Ports ==='
    netstat -tlnp | grep -E '(3306|6379|2000|2001|2002|8101|8201|8301)' | awk '{print \$4}' | sort
    echo ''
    
    echo '=== Process Summary ==='
    echo \"Game Server Processes: \$(ps aux | grep -E '(Server|server)' | grep -v grep | wc -l)\"
    echo \"SFW Framework Processes: \$(ps aux | grep -E '(sfw|SFW)' | grep -v grep | wc -l)\"
    echo ''
    
    echo '=== Port Status ==='
    netstat -tlnp | grep ':3306 ' && echo 'MySQL Port: OK' || echo 'MySQL Port: NOT LISTENING'
    netstat -tlnp | grep ':6379 ' && echo 'Redis Port: OK' || echo 'Redis Port: NOT LISTENING'
    netstat -tlnp | grep ':2000 ' && echo 'SFW Registry: OK' || echo 'SFW Registry: NOT LISTENING'
    netstat -tlnp | grep ':2001 ' && echo 'SFW Control: OK' || echo 'SFW Control: NOT LISTENING'
    netstat -tlnp | grep ':2002 ' && echo 'SFW Agent: OK' || echo 'SFW Agent: NOT LISTENING'
    netstat -tlnp | grep ':8101 ' && echo 'GateServer: OK' || echo 'GateServer: NOT LISTENING'
    netstat -tlnp | grep ':8201 ' && echo 'GameServer: OK' || echo 'GameServer: NOT LISTENING'
    netstat -tlnp | grep ':8301 ' && echo 'BattleServer: OK' || echo 'BattleServer: NOT LISTENING'
    echo ''
    
    echo '=== Log Files Created ==='
    find /data/applog -name '*.log' -type f | wc -l
    echo ''
    
    echo '=== Disk Usage ==='
    df -h /data
    echo ''
    
    # Run status dashboard if available
    if [ -f /data/game_status_dashboard.sh ]; then
        echo '=== STATUS DASHBOARD ==='
        /data/game_status_dashboard.sh
    fi
"

# Step 7: Check for errors
log_message "${YELLOW}=== STEP 7: CHECKING FOR ERRORS ===${NC}"
ssh "$SERVER_USER@$SERVER_IP" "
    echo '=== ERROR CHECK ==='
    if find /data/applog -name '*.log' -type f -exec grep -l -i error {} \; 2>/dev/null | head -5; then
        echo 'Found error logs - checking details:'
        find /data/applog -name '*.log' -type f -exec grep -i error {} \; 2>/dev/null | head -10
    else
        echo 'No error logs found'
    fi
"

# Final summary
log_message "${BLUE}========================================${NC}"
log_message "${BLUE}        DEPLOYMENT SUMMARY              ${NC}"
log_message "${BLUE}========================================${NC}"

log_message "${GREEN}✅ Auto deployment and restart completed!${NC}"
log_message ""
log_message "📋 Summary:"
log_message "• Server: $SERVER_IP"
log_message "• Backup: /data/backup/$TIMESTAMP"
log_message "• Scripts: Uploaded and configured"
log_message "• System: Restarted and verified"
log_message "• Log: $LOG_FILE"
log_message ""
log_message "🔧 Next steps:"
log_message "1. Review system status above"
log_message "2. Monitor logs: ssh $SERVER_USER@$SERVER_IP 'tail -f /data/applog/system_startup.log'"
log_message "3. Check dashboard: ssh $SERVER_USER@$SERVER_IP '/data/game_status_dashboard.sh'"
log_message ""
log_message "${GREEN}🎉 System is ready for operation!${NC}"
