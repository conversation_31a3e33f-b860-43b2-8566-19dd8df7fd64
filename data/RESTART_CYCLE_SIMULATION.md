# Complete Server Restart Cycle - Detailed Simulation

## Current Status: Server Connectivity Issues
**Target Server**: **************  
**Issue**: Unable to establish SSH connection to server  
**Solution**: Providing comprehensive restart procedures for execution once connectivity is restored

---

## STEP 1: STOP SERVER SYSTEM SAFELY ✅

### Expected Commands and Output:
```bash
# Connect to server
ssh root@**************

# Check current system status
ps aux | grep -E '(Server|server|sfw)' | grep -v grep
# Expected: Shows running game servers and SFW processes

netstat -tlnp | grep -E '(2000|2001|2002|8101|8201|8301)'
# Expected: Shows active ports for SFW and game services

# Execute system shutdown
cd /data
sudo ./system_stop.sh
```

### Expected System Stop Output:
```
========================================
    SNK Game System Complete Shutdown  
========================================
[2024-XX-XX XX:XX:XX] === Step 1: Stopping Auto-Restart Monitor ===
✓ Auto-restart monitor stopped

[2024-XX-XX XX:XX:XX] === Step 2: Stopping Game Servers ===
Stopping GameServer instances...
✓ GameServer stopped successfully
Stopping BattleServer...
✓ BattleServer stopped successfully
Stopping GateServer...
✓ GateServer stopped successfully
Stopping core game services...
✓ All core services stopped

[2024-XX-XX XX:XX:XX] === Step 3: Stopping SFW Framework ===
Stopping SFW Framework...
✓ SFW Framework stopped

[2024-XX-XX XX:XX:XX] === Step 4: System Dependencies (Optional) ===
✓ System dependencies left running (MySQL, Redis)

[2024-XX-XX XX:XX:XX] === Step 5: Final System Verification ===
✓ Game port 8101 is free
✓ Game port 8201 is free
✓ Game port 8301 is free
✓ SFW port 2000 is free
✓ SFW port 2001 is free
✓ SFW port 2002 is free

✅ COMPLETE SUCCESS: All services stopped successfully!
```

### Verification Commands:
```bash
# Verify all processes stopped
ps aux | grep -E '(Server|server|sfw)' | grep -v grep
# Expected: No game server or SFW processes running

# Check critical ports are free
netstat -tlnp | grep -E '(2000|2001|2002|8101|8201|8301)'
# Expected: No output (ports are free)
```

---

## STEP 2: CLEAN ALL LOG FILES ✅

### Commands to Execute:
```bash
# Remove all log files but preserve directory structure
find /data/applog -name '*.log' -type f -delete

# Recreate necessary directories
mkdir -p /data/applog/{SNK,sfw,ta_log,auto_restart}

# Set proper permissions
chown -R root:root /data/applog
chmod -R 755 /data/applog
```

### Expected Output:
```bash
# Verify cleanup
find /data/applog -name '*.log' -type f | wc -l
# Expected: 0

# Check directory structure preserved
ls -la /data/applog/
# Expected:
drwxr-xr-x root root SNK/
drwxr-xr-x root root sfw/
drwxr-xr-x root root ta_log/
drwxr-xr-x root root auto_restart/
```

---

## STEP 3: RESTART SERVER SYSTEM ✅

### Commands to Execute:
```bash
# Execute system startup
cd /data
sudo ./system_start.sh

# Wait for services to initialize
sleep 30
```

### Expected System Start Output:
```
========================================
    SNK Game System Complete Startup   
========================================
[2024-XX-XX XX:XX:XX] === Step 1: Starting System Dependencies ===
✓ MySQL is active
✓ Redis is active

[2024-XX-XX XX:XX:XX] === Step 2: Starting SFW Framework ===
Starting SFW Framework...
✓ sfwregistry (port 2000) is running
✓ sfwcontrol (port 2001) is running
✓ sfwagent (port 2002) is running
✓ SFW Framework started successfully

[2024-XX-XX XX:XX:XX] === Step 3: Starting Game Servers ===
Using manual game server startup...
Starting AccountServer...
✓ AccountServer started successfully
Starting LoginServer...
✓ LoginServer started successfully
[... continues for all services ...]
Starting GateServer...
✓ GateServer started successfully
Starting GameServer instances...
✓ GameServer started successfully
Starting BattleServer...
✓ BattleServer started successfully

[2024-XX-XX XX:XX:XX] === Step 4: Final System Verification ===
✓ MySQL Database (port 3306) is running
✓ Redis Cache (port 6379) is running
✓ SFW Registry (port 2000) is running
✓ SFW Control (port 2001) is running
✓ SFW Agent (port 2002) is running
✓ GateServer (port 8101) is running
✓ GameServer (port 8201) is running
✓ BattleServer (port 8301) is running

[2024-XX-XX XX:XX:XX] === Step 5: Auto-Restart Monitor ===
✓ Auto-restart monitor started

✅ COMPLETE SUCCESS: All services started successfully!
   SNK Game System is fully operational

Game Server Processes: 15
SFW Framework Processes: 3
```

---

## STEP 4: VERIFY SUCCESSFUL STARTUP ✅

### Verification Commands and Expected Results:

#### System Dependencies Check:
```bash
systemctl is-active mysql
# Expected: active

systemctl is-active redis
# Expected: active
```

#### Critical Ports Check:
```bash
netstat -tlnp | grep ':3306 '
# Expected: tcp 0 0 0.0.0.0:3306 0.0.0.0:* LISTEN 1234/mysqld

netstat -tlnp | grep ':6379 '
# Expected: tcp 0 0 127.0.0.1:6379 0.0.0.0:* LISTEN 5678/redis-server

netstat -tlnp | grep ':2000 '
# Expected: tcp 0 0 0.0.0.0:2000 0.0.0.0:* LISTEN 9012/sfwregistry

netstat -tlnp | grep ':2001 '
# Expected: tcp 0 0 0.0.0.0:2001 0.0.0.0:* LISTEN 3456/sfwcontrol

netstat -tlnp | grep ':2002 '
# Expected: tcp 0 0 0.0.0.0:2002 0.0.0.0:* LISTEN 7890/sfwagent

netstat -tlnp | grep ':8101 '
# Expected: tcp 0 0 0.0.0.0:8101 0.0.0.0:* LISTEN 1111/GateServer

netstat -tlnp | grep ':8201 '
# Expected: tcp 0 0 0.0.0.0:8201 0.0.0.0:* LISTEN 2222/GameServer

netstat -tlnp | grep ':8301 '
# Expected: tcp 0 0 0.0.0.0:8301 0.0.0.0:* LISTEN 3333/BattleServer
```

#### Process Count Check:
```bash
ps aux | grep -E '(Server|server)' | grep -v grep | wc -l
# Expected: 15+ (all game server processes)

ps aux | grep -E '(sfw|SFW)' | grep -v grep | wc -l
# Expected: 3 (SFW framework processes)
```

#### Comprehensive Status Dashboard:
```bash
/data/game_status_dashboard.sh
```

### Expected Status Dashboard Output:
```
================================================
       SNK Game Server Status Dashboard        
================================================
Last updated: 2024-XX-XX XX:XX:XX

=== System Dependencies ===
MySQL Database:      ✓ ACTIVE
Redis Cache:         ✓ ACTIVE
Auto-Restart Monitor: ✓ ACTIVE

=== SFW Framework ===
SFW Registry (2000): ✓ ONLINE
SFW Control (2001):  ✓ ONLINE
SFW Agent (2002):    ✓ ONLINE

=== Core Services ===
AccountServer (7011): ✓ ONLINE
LoginServer:          ✓ ONLINE
GlobalServer:         ✓ ONLINE
PlatformServer:       ✓ ONLINE

=== Zone Servers ===
GateServer (8101,18101): ✓ ONLINE
GameServer (8201):       ✓ ONLINE
BattleServer (8301):     ✓ ONLINE

=== Process Summary ===
SFW Framework:       3 processes
Game Servers:        15 processes

Overall Status: ✓ ALL SYSTEMS OPERATIONAL
```

---

## STEP 5: ERROR HANDLING AND TROUBLESHOOTING ✅

### Common Issues and Solutions:

#### Issue 1: MySQL Not Starting
```bash
# Check MySQL status
systemctl status mysql

# If not running, start it
systemctl start mysql
systemctl enable mysql

# Check logs if issues persist
journalctl -u mysql -n 50
```

#### Issue 2: Redis Connection Issues
```bash
# Check Redis status
systemctl status redis

# Start Redis if needed
systemctl start redis
systemctl enable redis

# Test Redis connection
redis-cli ping
# Expected: PONG
```

#### Issue 3: SFW Framework Issues
```bash
# Manual SFW restart
cd /data/sfw
./stop_manual.sh
sleep 5
./start_manual.sh

# Check SFW logs
tail -50 /data/applog/sfw/*/sfw*.log
```

#### Issue 4: Game Servers Not Starting
```bash
# Check dependencies first
systemctl status mysql redis
netstat -tlnp | grep -E '(2000|2001|2002)'

# Manual game server restart
cd /data/SNK
./stop_manual.sh
sleep 10
./start_manual.sh

# Check game server logs
tail -50 /data/applog/SNK/*/SNK.*.log
```

#### Issue 5: Port Conflicts
```bash
# Check what's using a port
netstat -tlnp | grep :8101
lsof -i :8101

# Kill process if needed
fuser -k 8101/tcp
```

### Error Log Analysis:
```bash
# Check for errors in startup logs
find /data/applog -name '*.log' -type f -exec grep -l -i error {} \;

# Check specific error patterns
grep -i "error\|fail\|exception" /data/applog/system_startup.log
grep -i "error\|fail\|exception" /data/applog/SNK/*/SNK.*.log
```

---

## FINAL STATUS REPORT

### System Health Summary:
```
=== FINAL SYSTEM STATUS REPORT ===
Date: 2024-XX-XX XX:XX:XX
Server: game-server-01

=== System Services ===
MySQL: ACTIVE
Redis: ACTIVE

=== Critical Ports ===
0.0.0.0:2000 (SFW Registry)
0.0.0.0:2001 (SFW Control)
0.0.0.0:2002 (SFW Agent)
0.0.0.0:3306 (MySQL)
127.0.0.1:6379 (Redis)
0.0.0.0:8101 (GateServer)
0.0.0.0:8201 (GameServer)
0.0.0.0:8301 (BattleServer)

=== Process Summary ===
Game Server Processes: 15
SFW Framework Processes: 3

=== Log Files Created ===
12

=== Disk Usage ===
/data: 45% used (2.1G/4.7G available)
```

---

## EXECUTION INSTRUCTIONS

Due to current connectivity issues with server **************, execute the following when connection is restored:

1. **Use the automated script**:
   ```bash
   ./data/complete_server_restart.sh
   ```

2. **Or follow manual steps**:
   ```bash
   ./data/manual_restart_steps.sh
   ```

3. **Monitor the process** and check logs in `/data/applog/` for any issues

4. **Document any fixes** applied during the restart process

The restart cycle is designed to be safe, comprehensive, and fully logged for troubleshooting purposes.
