#!/bin/bash

# SNK Game System Complete Shutdown Script
# Author: Auto-generated for SNK Game Management
# Description: Stops the complete SNK game system in proper order

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/data/applog/system_shutdown.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
set -e
trap 'log_message "${RED}ERROR: System shutdown encountered error at line $LINENO${NC}"' ERR

log_message "${BLUE}========================================${NC}"
log_message "${BLUE}    SNK Game System Complete Shutdown  ${NC}"
log_message "${BLUE}========================================${NC}"

# Function to check service status
check_service() {
    local service=$1
    if systemctl is-active --quiet "$service" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Function to check port
check_port() {
    local port=$1
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        return 0
    else
        return 1
    fi
}

# Step 1: Stop Auto-Restart Monitor
log_message "${YELLOW}=== Step 1: Stopping Auto-Restart Monitor ===${NC}"

if check_service auto_restart; then
    log_message "Stopping auto-restart monitor..."
    systemctl stop auto_restart
    sleep 3
    if ! check_service auto_restart; then
        log_message "${GREEN}✓ Auto-restart monitor stopped${NC}"
    else
        log_message "${YELLOW}⚠ Auto-restart monitor may still be running${NC}"
    fi
else
    log_message "${GREEN}✓ Auto-restart monitor is not running${NC}"
fi

# Step 2: Stop Game Servers
log_message "${YELLOW}=== Step 2: Stopping Game Servers ===${NC}"

cd "$SCRIPT_DIR/SNK"
if [ -f "stop_manual.sh" ]; then
    log_message "Using manual game server shutdown..."
    ./stop_manual.sh
else
    log_message "Using automatic game server shutdown..."
    ./stop.sh
fi

# Additional cleanup for any remaining game processes
log_message "Performing additional game server cleanup..."
game_processes=("AccountServer" "LoginServer" "GlobalServer" "PlatformServer" "BgipProxy" "ForwardServer" "FriendServer" "MatchServer" "PlayServer" "RankServer" "RegionServer" "TeamServer" "IAPServer" "GameServer" "BattleServer" "GateServer")

for process in "${game_processes[@]}"; do
    if pgrep -f "$process" > /dev/null; then
        log_message "Cleaning up remaining $process processes..."
        pkill -f "$process" 2>/dev/null || true
        sleep 1
    fi
done

# Step 3: Stop SFW Framework
log_message "${YELLOW}=== Step 3: Stopping SFW Framework ===${NC}"

# Check if SFW is running
sfw_running=false
if check_port 2000 || check_port 2001 || check_port 2002; then
    sfw_running=true
fi

if [ "$sfw_running" = true ]; then
    log_message "Stopping SFW Framework..."
    cd "$SCRIPT_DIR/sfw"
    if [ -f "stop_manual.sh" ]; then
        ./stop_manual.sh
    else
        ./stop.sh
    fi
    
    # Additional SFW cleanup
    sleep 3
    sfw_processes=("sfwagent" "sfwcontrol" "sfwregistry")
    for process in "${sfw_processes[@]}"; do
        if pgrep -f "$process" > /dev/null; then
            log_message "Force stopping remaining $process..."
            pkill -f "$process" 2>/dev/null || true
            sleep 1
        fi
    done
    
    log_message "${GREEN}✓ SFW Framework stopped${NC}"
else
    log_message "${GREEN}✓ SFW Framework is not running${NC}"
fi

# Step 4: Stop System Dependencies (Optional)
log_message "${YELLOW}=== Step 4: System Dependencies (Optional) ===${NC}"

# Note: We typically don't stop MySQL and Redis as they may be used by other services
# Uncomment the following lines if you want to stop them as well

# Stop Redis (optional)
# if check_service redis; then
#     log_message "Stopping Redis service..."
#     systemctl stop redis
#     sleep 2
#     if ! check_service redis; then
#         log_message "${GREEN}✓ Redis stopped${NC}"
#     else
#         log_message "${YELLOW}⚠ Redis may still be running${NC}"
#     fi
# else
#     log_message "${GREEN}✓ Redis is not running${NC}"
# fi

# Stop MySQL (optional)
# if check_service mysql; then
#     log_message "Stopping MySQL service..."
#     systemctl stop mysql
#     sleep 3
#     if ! check_service mysql; then
#         log_message "${GREEN}✓ MySQL stopped${NC}"
#     else
#         log_message "${YELLOW}⚠ MySQL may still be running${NC}"
#     fi
# else
#     log_message "${GREEN}✓ MySQL is not running${NC}"
# fi

log_message "${GREEN}✓ System dependencies left running (MySQL, Redis)${NC}"

# Step 5: Final System Verification
log_message "${YELLOW}=== Step 5: Final System Verification ===${NC}"

# Check game server ports
game_ports=("8101" "8201" "8301" "7011")
ports_still_used=0

for port in "${game_ports[@]}"; do
    if check_port "$port"; then
        log_message "${YELLOW}⚠ Game port $port still in use${NC}"
        ((ports_still_used++))
    else
        log_message "${GREEN}✓ Game port $port is free${NC}"
    fi
done

# Check SFW ports
sfw_ports=("2000" "2001" "2002")
sfw_ports_used=0

for port in "${sfw_ports[@]}"; do
    if check_port "$port"; then
        log_message "${YELLOW}⚠ SFW port $port still in use${NC}"
        ((sfw_ports_used++))
    else
        log_message "${GREEN}✓ SFW port $port is free${NC}"
    fi
done

# Check remaining processes
remaining_game_processes=$(ps aux | grep -E "(Server|server)" | grep -v grep | grep -v "system_stop.sh" | wc -l)
remaining_sfw_processes=$(ps aux | grep -E "(sfw|SFW)" | grep -v grep | grep -v "system_stop.sh" | wc -l)

# Final Summary
log_message "${BLUE}========================================${NC}"
log_message "${BLUE}        System Shutdown Summary         ${NC}"
log_message "${BLUE}========================================${NC}"

if [ "$remaining_game_processes" -eq 0 ] && [ "$remaining_sfw_processes" -eq 0 ] && [ "$ports_still_used" -eq 0 ] && [ "$sfw_ports_used" -eq 0 ]; then
    log_message "${GREEN}✅ COMPLETE SUCCESS: All services stopped successfully!${NC}"
    log_message "${GREEN}   SNK Game System is completely shut down${NC}"
elif [ "$remaining_game_processes" -eq 0 ] && [ "$remaining_sfw_processes" -eq 0 ]; then
    log_message "${YELLOW}⚠ MOSTLY SUCCESS: All processes stopped${NC}"
    log_message "${YELLOW}   Some ports may still be in TIME_WAIT state${NC}"
else
    log_message "${YELLOW}⚠ PARTIAL SUCCESS: Some processes may still be running${NC}"
    
    if [ "$remaining_game_processes" -gt 0 ]; then
        log_message "${YELLOW}   Remaining game processes: $remaining_game_processes${NC}"
    fi
    
    if [ "$remaining_sfw_processes" -gt 0 ]; then
        log_message "${YELLOW}   Remaining SFW processes: $remaining_sfw_processes${NC}"
    fi
fi

log_message "${BLUE}=== System Status ===${NC}"
log_message "Game Server Processes: $remaining_game_processes"
log_message "SFW Framework Processes: $remaining_sfw_processes"
log_message "Game Ports in Use: $ports_still_used"
log_message "SFW Ports in Use: $sfw_ports_used"

log_message "${BLUE}=== Quick Commands ===${NC}"
log_message "Start System:      /data/system_start.sh"
log_message "System Status:     /data/game_status_dashboard.sh"
log_message "View Logs:         tail -f /data/applog/system_shutdown.log"

log_message "${BLUE}=== System Shutdown Complete ===${NC}"
