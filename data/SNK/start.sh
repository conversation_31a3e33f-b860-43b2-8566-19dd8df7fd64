#!/bin/bash

# SNK Game Server Automatic Start Script (Fixed)
# Author: Auto-generated for SNK Game Management
# Description: Automatically starts all game servers with fixed paths

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/data/applog/startup_auto.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log_message "${GREEN}=== SNK Game Server Automatic Startup ===${NC}"

# Clear old logs
sudo rm -rf /data/applog/SNK/*
mkdir -p /data/applog/ta_log

# Start core services first
log_message "Starting core game services..."
for dir in */; do
    # Remove trailing /
    dir=${dir%/}

    # Skip directories containing dots (zone-specific directories)
    if [[ "$dir" == *.* ]]; then
        log_message "Skipping zone directory: $dir"
        continue
    fi

    # Check if start.sh exists in directory
    if [ -f "$dir/start.sh" ]; then
        log_message "Starting service in directory: $dir"
        (
            cd "$dir" || exit
            ./start.sh
            sleep 2
        )
    else
        log_message "No start.sh found in directory: $dir"
    fi
done

# Start zone-specific services with fixed paths
log_message "Starting zone-specific services..."

# Start GateServer (FIXED PATH)
if [ -d "$SCRIPT_DIR/snk.zone.101/GateServer" ]; then
    log_message "Starting GateServer..."
    cd "$SCRIPT_DIR/snk.zone.101/GateServer"
    ./start.sh 1
    sleep 3
else
    log_message "${RED}ERROR: GateServer directory not found${NC}"
fi

# Start BattleServer (FIXED PATH)
if [ -d "$SCRIPT_DIR/snk.battle.101/BattleServer" ]; then
    log_message "Starting BattleServer..."
    cd "$SCRIPT_DIR/snk.battle.101/BattleServer"
    ./start.sh
    sleep 3
else
    log_message "${RED}ERROR: BattleServer directory not found${NC}"
fi

# Start GameServer (FIXED PATH)
if [ -d "$SCRIPT_DIR/snk.zone.101/GameServer" ]; then
    log_message "Starting GameServer instances..."
    cd "$SCRIPT_DIR/snk.zone.101/GameServer"
    ./startall.sh
    sleep 5
else
    log_message "${RED}ERROR: GameServer directory not found${NC}"
fi

# Return to original directory
cd "$SCRIPT_DIR"

log_message "${GREEN}=== Automatic Startup Complete ===${NC}"
log_message "Check individual service logs in /data/applog/SNK/ for details"
