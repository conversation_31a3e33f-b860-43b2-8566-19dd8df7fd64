<sfw>
    <client>
        locator                   = sfw.sfwregistry.QueryObj@tcp -h 127.0.0.1 -p 2000 -t 100000
        refresh-endpoint-interval = 60000
        sync-invoke-timeout       = 3000
        async-invoke-timeout      = 5000
        connect-timeout           = 1000
        asyncthread               = 3
    </client>

    <server>
        app      = SNK
        server   = ForwardServer
        logpath  = /data/applog
        loglevel = DEBUG
        loop-interval = 100
        agent-report = sfw.sfwagent.AgentReportObj@tcp -h 127.0.0.1 -p 2002 -t 3600000

        <Service_1>
            service    = SNK.ForwardServer.ForwardServiceObj
            endpoint   = tcp -h 127.0.0.1 -p 7021 -t 60000
            protocol   = sfw
            threads    = 20
            maxconns   = 1024
            queuecap   = 10240
            queuetimeout = 5000
        </Service_1>
    </server>
</sfw>
