#!/bin/bash

# SNK Game Server Manual Stop Script
# Author: Auto-generated for SNK Game Management
# Description: Manually stops game servers with proper shutdown procedures

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SHUTDOWN_LOG="/data/applog/shutdown_manual.log"
MAX_WAIT_TIME=30
FORCE_KILL_TIMEOUT=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$SHUTDOWN_LOG"
}

# Error handling
set -e
trap 'log_message "${RED}ERROR: Script terminated unexpectedly at line $LINENO${NC}"' ERR

log_message "${BLUE}=== SNK Game Server Manual Shutdown ===${NC}"
log_message "Starting manual game server shutdown sequence..."

# Function to wait for process to stop
wait_for_process_stop() {
    local process_name=$1
    local max_wait=$2
    local count=0
    
    while [ $count -lt $max_wait ]; do
        if ! pgrep -f "$process_name" > /dev/null; then
            return 0
        fi
        sleep 1
        ((count++))
    done
    return 1
}

# Function to gracefully stop a service
stop_service() {
    local service_dir=$1
    local service_name=$2
    
    if [ -d "$service_dir" ] && [ -f "$service_dir/stop.sh" ]; then
        log_message "Stopping $service_name..."
        cd "$service_dir"
        ./stop.sh
        
        # Wait for process to stop
        if wait_for_process_stop "$service_name" 15; then
            log_message "${GREEN}✓ $service_name stopped successfully${NC}"
        else
            log_message "${YELLOW}⚠ $service_name did not stop gracefully, forcing...${NC}"
            pkill -f "$service_name" 2>/dev/null || true
            sleep 2
        fi
    else
        log_message "${YELLOW}⚠ $service_name stop script not found${NC}"
    fi
}

# Function to force kill processes
force_kill_processes() {
    local pattern=$1
    local description=$2
    
    log_message "Force killing $description processes..."
    pkill -f "$pattern" 2>/dev/null || true
    sleep 2
    
    # Verify processes are gone
    if pgrep -f "$pattern" > /dev/null; then
        log_message "${YELLOW}⚠ Some $description processes still running, using SIGKILL...${NC}"
        pkill -9 -f "$pattern" 2>/dev/null || true
        sleep 1
    fi
}

# Stop zone-specific services first (reverse order of startup)
log_message "${YELLOW}=== Stopping Zone Services ===${NC}"

# Stop GameServer instances
if [ -d "$SCRIPT_DIR/snk.zone.101/GameServer" ]; then
    log_message "Stopping GameServer instances..."
    cd "$SCRIPT_DIR/snk.zone.101/GameServer"
    if [ -f "stopall.sh" ]; then
        ./stopall.sh
    else
        force_kill_processes "GameServer" "GameServer"
    fi
    sleep 3
fi

# Stop BattleServer
if [ -d "$SCRIPT_DIR/snk.battle.101/BattleServer" ]; then
    stop_service "$SCRIPT_DIR/snk.battle.101/BattleServer" "BattleServer"
fi

# Stop GateServer
if [ -d "$SCRIPT_DIR/snk.zone.101/GateServer" ]; then
    log_message "Stopping GateServer..."
    cd "$SCRIPT_DIR/snk.zone.101/GateServer"
    if [ -f "stopall.sh" ]; then
        ./stopall.sh
    else
        stop_service "$SCRIPT_DIR/snk.zone.101/GateServer" "GateServer"
    fi
fi

# Stop core game services (reverse order of startup)
log_message "${YELLOW}=== Stopping Core Game Services ===${NC}"

core_services=(
    "IAPServer"
    "TeamServer"
    "RegionServer"
    "RankServer"
    "PlayServer"
    "MatchServer"
    "FriendServer"
    "ForwardServer"
    "BgipProxy"
    "PlatformServer"
    "GlobalServer"
    "LoginServer"
    "AccountServer"
)

# Stop core services
for service in "${core_services[@]}"; do
    stop_service "$SCRIPT_DIR/$service" "$service"
done

# Force kill any remaining game server processes
log_message "${YELLOW}=== Cleaning Up Remaining Processes ===${NC}"

# Define process patterns to clean up
process_patterns=(
    "AccountServer"
    "LoginServer"
    "GlobalServer"
    "PlatformServer"
    "BgipProxy"
    "ForwardServer"
    "FriendServer"
    "MatchServer"
    "PlayServer"
    "RankServer"
    "RegionServer"
    "TeamServer"
    "IAPServer"
    "GameServer"
    "BattleServer"
    "GateServer"
)

for pattern in "${process_patterns[@]}"; do
    if pgrep -f "$pattern" > /dev/null; then
        log_message "${YELLOW}Found remaining $pattern processes, cleaning up...${NC}"
        force_kill_processes "$pattern" "$pattern"
    fi
done

# Optional: Stop SFW Framework (commented out by default)
# Uncomment the following lines if you want to stop SFW Framework as well
# log_message "${YELLOW}=== Stopping SFW Framework (Optional) ===${NC}"
# cd /data/sfw
# ./stop.sh

# Final verification
log_message "${YELLOW}=== Final Cleanup Verification ===${NC}"

remaining_processes=$(ps aux | grep -E "(Server|server)" | grep -v grep | grep -v "stop_manual.sh" | wc -l)

if [ "$remaining_processes" -eq 0 ]; then
    log_message "${GREEN}✅ All game server processes stopped successfully!${NC}"
else
    log_message "${YELLOW}⚠ $remaining_processes processes may still be running:${NC}"
    ps aux | grep -E "(Server|server)" | grep -v grep | grep -v "stop_manual.sh" | while read line; do
        log_message "  $line"
    done
fi

# Show port status
log_message "${BLUE}=== Port Status After Shutdown ===${NC}"
game_ports=("8101" "8201" "8301" "7011")
for port in "${game_ports[@]}"; do
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        log_message "${YELLOW}⚠ Port $port still in use${NC}"
    else
        log_message "${GREEN}✓ Port $port is free${NC}"
    fi
done

log_message "${BLUE}=== Manual Shutdown Complete ===${NC}"
log_message "Use './start_manual.sh' to start all services"
log_message "Use '/data/game_status_dashboard.sh' to monitor system status"
