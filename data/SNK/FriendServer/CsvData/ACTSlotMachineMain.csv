系统ID SystemBase.ID,批量抽奖最大次数,代币,抽奖一次消耗的道具,初始结果 ACTSlotMachineResult.ID 必须使用组合数为3的结果,抽奖一次增加的保底值,总保底值,保底结果 ACTSlotMachineResult.ID 必须使用组合数为3的结果,邮件ID 活动结束补发奖励,邮件ID 排行榜结算,排行榜上榜 岁币门槛数量,距离活动开始多少时间后结算排行榜（秒）
ID,OnceMaxTimes,DaiBi,CostItem,DefaultResult,GuaranteeOnce,GuaranteeMax,GuaranteeResult,ProgressMailID,RankMailID,RankLowLimit,RankEndTime
,,,,,,,,,,,
int,int,class:SItemNoCnt[int:type;int:id],vector:SItem[int:type;int:id;int:count],int,int,int,int,int,int,int,int
77004,10,1:1000513,1:1000512:1,77004013,1,100,77004001,1179,1180,20000,838800
79007,10,1:1000607,1:1000606:1,79007013,1,100,79007001,1217,1218,10000,579600
