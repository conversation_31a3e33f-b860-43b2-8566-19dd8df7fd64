ID,活动1阶段 - 模拟赛,活动2阶段 - 16进8,活动3阶段 - 8进4,活动4阶段 - 4进2,活动5阶段 - 决赛,活动6阶段 - 发奖,开启间隔时间,押注提前几分钟截止,排行榜邮件ID,参与消耗的道具,每次获得保底道具,押中获得好运点,每个活动阶段的倍数,模拟赛押中获得期望产出,开启时间,距离活动开始多少时间后结算排行榜（秒）
SystemID,StageTime1,StageTime2,StageTime3,StageTime4,StageTime5,StageTime6,IntervalTime,Time,MailID,CostItem,BasicRewards,Integral1,Multiples,ExpectedOutput1,StartTime,RankEndTime
,,,,,,,,,,,,,,,,
int,int,int,int,int,int,int,int,int,int,vector:SItem[int:type;int:id;int:count],vector:SItem[int:type;int:id;int:count],vector:SItem[int:type;int:id;int:count],vector:int[],int,string,int
77022,432000,27000,28800,57600,28800,30600,14,300,1207,1:500009:100;,1:500010:100;,1:500011:1;,1;1;2;4;8;,95,2023-02-27 05:00:00,579600
