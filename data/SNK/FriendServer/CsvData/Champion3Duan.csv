索引ID,段位差距,是否能匹配机器人,大段位分段,中段位分段,星星数,到达是否广播,晋升段位保护次数,赛季结束重置到的段位,失败是否扣星,段位场景,几场不会匹配同一对手,可以匹配的星级下限,可以匹配的星级上限,星级下限扩大步长,星级上限扩大步长,初始战力下限,初始战力上限,战力下限扩大步长,战力上限扩大步长,战力修正系数1,战力修正系数2,战力修正系数3,只匹配机器人,相应的升段积分,积分下限扩大步长,积分上限扩大步长,附加积分参数
ID,DuanDiff,CanMatchRobot,DuanLarge,DuanMid,DuanStarNum,Isbroadcast,DuanUpProtectTimes,DuanResetRule,LoseToStar,SceneID,RepeatMatchCD,MatchDuanLow,MatchDuanHigh,MatchDuanLowStep,MatchDuanHighStep,MatchCombatLow,MatchCombatHigh,MatchCombatLowStep,MatchCombatHighStep,MatchCombatFactor1,MatchCombatFactor2,MatchCombatFactor3,OnlyMatchBot,ChampionRankScore,MatchScoreLowStep,MatchScoreHighStep,ChampionArg
range(0:n),,range(0:1),range(0:n),range(0:n),range(0:n),range(0:n),range(0:n),,,cfg(SceneStencil.csv:ID),range(0:n),,,range(0:n),range(0:n),range(0:99),range(101:n),range(0:n),range(0:n),range(0:n),range(0:n),range(0:n),range(0:n),,,,
int,int,int,int,int,int,int,int,int,bool,int,int,int,int,vector:int[],vector:int[],int,int,vector:int[],vector:int[],int,int,int,int,int,vector:int[],vector:int[],int
1,1,1,1,1,0,0,0,1,1,1027,0,1,9,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,70,20,1,1000,25;50;75,0;0;10,3
2,1,1,1,1,1,0,3,1,1,1027,0,1,9,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,70,20,1,1100,25;50;75,0;0;10,3
3,1,1,1,1,2,0,3,1,1,1027,0,1,9,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,70,20,1,1200,25;50;75,0;0;10,4
4,1,1,2,1,3,0,3,1,1,1026,0,1,13,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,70,20,0,1300,25;50;75,0;0;10,4
5,2,1,2,2,0,0,3,1,1,1026,0,1,13,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,80,20,0,1400,25;50;75,0;0;10,4
6,2,1,2,2,1,0,3,1,1,1026,0,1,13,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,80,20,0,1500,25;50;75,0;0;10,5
7,2,1,3,2,2,0,3,1,1,1055,0,1,13,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,80,20,0,1600,25;50;75,0;0;10,5
8,2,1,3,2,3,0,3,1,1,1055,0,1,13,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,80,20,0,1700,25;50;75,0;0;10,5
9,3,1,3,3,0,0,3,1,1,1055,0,1,19,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,90,20,0,1800,25;50;75,0;0;10,6
10,3,1,4,3,1,0,3,4,1,1012,0,1,19,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,90,20,0,1900,25;50;75,0;0;10,6
11,3,1,4,3,2,0,3,4,1,1012,0,1,19,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,90,20,0,2000,25;50;75,0;0;10,6
12,3,1,4,3,3,0,3,4,1,1012,0,1,19,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,90,20,0,2100,25;50;75,0;0;10,7
13,4,1,5,1,0,0,3,4,1,1049,0,5,27,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,100,20,0,2200,75;100;125,0;0;25,7
14,4,1,5,1,1,0,0,4,1,1049,0,5,27,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,100,20,0,2300,75;100;125,0;0;25,8
15,4,1,5,1,2,0,0,4,1,1049,0,5,27,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,100,20,0,2400,75;100;125,0;0;25,8
16,4,1,6,1,3,0,0,7,1,1047,0,5,27,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,100,20,0,2500,75;100;125,0;0;25,9
17,4,1,6,1,4,0,0,7,1,1047,0,5,27,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,100,20,0,2600,75;100;125,0;0;25,9
18,5,1,6,2,0,0,0,7,1,1047,0,9,32,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,100,20,0,2700,75;100;125,0;0;25,10
19,5,1,7,2,1,1,0,7,1,1047,0,9,32,1;5;10;15;30,1;5;10;15;30,0,1000,1;5;10;15;30,1;5;10;15;30,50,100,20,0,2800,75;100;125,0;0;25,10
