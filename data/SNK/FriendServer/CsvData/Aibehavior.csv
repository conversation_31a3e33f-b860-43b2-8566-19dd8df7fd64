ID,条件,目标选择,cd类型,前置冷却(回合),cd值,共享冷却行为编号,行动概率,行为类型,是否切回合,行为ID,释放BuffId
ID,Condition,Aim,Cdtype,PreCoolDown,Cooldown,ShareCDType,Actionpercent,Actiontype,RoundEnd,Actionid,SkillEffect
range(0:n),,,,,,,,,,,
int,string,string,string,int,int,vector:int[],string,string,int,vector:int[],vector:SSkillEffect[int:camp;int:targetid;int:left;int:right;int:prob;int:countid]
100000,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,10000,skillgroup,1,1,
100001,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,10000,skillgroup,1,1,
100002,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,6000,skillgroup,1,3,
101001,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,10000,skillgroup,1,1,
102001,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,5000,skillgroup,1,2,
102002,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=1&attr(curhp/maxhp)<0.6;attr(battle_camp)=0,skillaim,skillcd,0,0,,10000,skillgroup,1,2,
102101,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5)&myself(attr(silent)=0)&myself(attr(fury)>=100);attr(battle_camp)=0,skillaim,skillcd,0,0,,10000,skillgroup,1,2,
103101,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5)&myself(attr(silent)=0)&myself(attr(fury)>=100);attr(battle_camp)=0,skillaim,skillcd,0,0,,10000,skillgroup,1,3,
103102,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5)&myself(attr(silent)=0)&myself(attr(fury)>=100);attr(battle_camp)=1&attr(curhp/maxhp)<0.6;attr(battle_camp)=0,skillaim,skillcd,0,0,,10000,skillgroup,1,3,
103103,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5)&myself(attr(silent)=0)&myself(attr(fury)>=100)&myself(attr((curhp/maxhp)<0.75);attr(battle_camp)=0,skillaim,skillcd,0,0,,10000,skillgroup,1,3,
103104,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5)&myself(attr(silent)=0)&myself(attr(fury)>=100);attr(battle_camp)=0,skillaim,skillcd,0,0,,5000,skillgroup,1,3,
106101,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5)&myself(attr(silent)=0)&myself(attr(fury)>=100));attr(battle_camp)=0,myself,behaviorcd,0,1,,10000,skillgroup,0,6,
106105,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=1;attr(battle_camp)=0&attr(curhp)>0&attr(curhp/maxhp)<0.6;attr(battle_camp)=0,skillaim,skillcd,0,0,,10000,skillgroup,1,3,
108001,myself(attr(active)=0);attr(battle_camp)=0&attr(active)=1;attr(battle_camp)=1,skillaim,behaviorcd,0,0,,10000,skillgroup,0,8,
108002,myself(attr(active)=0);attr(battle_camp)=0&attr(active)=1;attr(battle_camp)=0&attr(curhp)>0&attr(curhp/maxhp)<0.6;attr(battle_camp)=1,skillaim,behaviorcd,0,0,,10000,skillgroup,0,8,
108003,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5)&myself(attr(silent)=0)&myself(attr(fury)>=200));attr(battle_camp)=0,myself,behaviorcd,0,1,,10000,skillgroup,0,6,
108004,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5)&myself(attr(silent)=0)&myself(attr(fury)>=100));attr(battle_camp)=0;attr(battle_camp)=0&attr(curhp/maxhp)<0.5,myself,behaviorcd,0,1,,10000,skillgroup,0,6,
108005,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5)&myself(attr(silent)=0)&myself(attr(fury)>=150);attr(battle_camp)=0,skillaim,skillcd,0,0,,10000,skillgroup,1,3,
111001,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=1,skillaim,skillcd,0,0,,10000,skillgroup,1,3,
111002,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=1,skillaim,skillcd,0,0,,10000,skillgroup,1,3,
111003,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5)&myself(attr(silent)=0)&myself(attr(fury)>=100);attr(battle_camp)=1,skillaim,skillcd,0,0,,10000,skillgroup,1,3,
111004,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5)&myself(attr(silent)=0)&myself(attr(fury)>=100);attr(battle_camp)=1,skillaim,skillcd,0,0,,10000,skillgroup,1,3,
111005,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=1,skillaim,skillcd,0,0,,10000,skillgroup,1,1,
111006,myself(attr(curhp)>0)&myself(attr(state)!=5)&myself(attr(silent)=0)&myself(attr(fury)>=100);attr(battle_camp)=1,skillaim,skillcd,0,0,,10000,skillgroup,1,3,
200001,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,10000,skillgroup,1,1,
200003,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,7500,skillgroup,1,3,
200006,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);,myself,behaviorcd,0,1,,10000,skillgroup,0,6,
300011,myself(fury)>=100,skillaim,skillcd,0,0,,10000,skill,0,1400111,
800001,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);,myself,behaviorcd,0,1,,5000,skillgroup,0,6,
800002,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,5000,skillgroup,1,1,
800003,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,5000,skillgroup,1,3,
800004,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,5000,skillgroup,1,1,
800005,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,5000,skillgroup,1,3,
800006,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,5000,skillgroup,1,1,
800007,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,5000,skillgroup,1,21,
800008,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,5000,skillgroup,1,22,
800009,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,5000,skillgroup,1,20,
800010,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,5000,skillgroup,1,23,
800011,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=0,skillaim,skillcd,0,0,,5000,skillgroup,1,20,
900001,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5);attr(battle_camp)=1,skillaim,skillcd,0,0,,10000,skillgroup,1,20,
900003,myself(attr(active)=1)&myself(attr(curhp)>0)&myself(attr(state)!=5)&myself(attr(silent)=0)&myself(attr(fury)>=100);attr(battle_camp)=1,skillaim,skillcd,0,0,,10000,skillgroup,1,22,
900007,myself(attr(curhp)>0);attr(battle_camp)=1&attr(active)=1|attr(battle_camp)=0&attr(active)=1;attr(battle_camp)=0,skillaim,skillcd,0,0,,10000,skillgroup,0,12,
