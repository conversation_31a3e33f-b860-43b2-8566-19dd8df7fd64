拍卖ID,拍卖类型,拍卖时间 星期几：协会准备：协会开启：协会结束：世界开启：世界结束,货品组,分红下限,基础钻石A,参数B 万分比,分红倍率C 万分比,分红上限,系统ID,周末额外分红百分比
Id,BidHouseType,BidTime,GoodsGroup,BaseBonus,Diamond_A,Parameter_a,Parameter_b,MaxBonus,SystemId,WeekendBonus
,,,,,,,,,,
int,int,vector:SBidTime[int:day;int:prepare;int:bidstart;int:bidend;int:worldstart;int:worldend],int,int,int,int,int,int,int,int
3,3,1:600:25200:54000:54900:61200;2:600:25200:54000:54900:61200;3:600:25200:54000:54900:61200;4:600:25200:54000:54900:61200;5:600:25200:54000:54900:61200;6:600:25200:54000:54900:61200;0:600:25200:54000:54900:61200;,3,100,4000,300000,15000,1000,40300,20
