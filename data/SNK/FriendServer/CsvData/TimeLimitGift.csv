礼包ID,开启时间,结束时间,触发条件枚举,触发条件参数,触发条件目标,触发条件枚举,触发条件参数,触发条件目标,触发条件枚举,触发条件参数,触发条件目标,前置礼包ID,互斥ID,道具ID,SDKID,折扣数值,存在时长
ID,StartTime,EndTime,CondType1,CondParam1,CondTarget1,CondType2,CondParam2,CondTarget2,CondType3,CondParam3,CondTarget3,PrevID,Group,Reward,SDKID,Discount,Time
,,,,,,,,,,,,,,cfg_threshold(CheckItemMaxCount.csv:Type;ItemID;Num),,,
int,int,int,class:SCondType[int:type;string:type_desc],vector:int[],int,class:SCondType[int:type;string:type_desc],vector:int[],int,class:SCondType[int:type;string:type_desc],vector:int[],int,int,int,vector:SItem[int:type;int:id;int:count],int,int,int
10100101,1,0,30301001:主角等级达到m级,,15,,,0,,,0,0,0,1:1020002:300;1:1020102:10;,10401001,900,28800
10100201,1,0,30301001:主角等级达到m级,,26,30500004:[核心] - [当日] - 打造一个a阶b品质以上核心,0;0,1,,,0,0,0,1:1020002:300;1:1000028:2;,10401002,600,28800
10100301,1,0,30301001:主角等级达到m级,,30,,,0,,,0,0,0,1:1020002:300;1:450001:250000;,10401003,900,28800
10100401,1,0,30301001:主角等级达到m级,,30,,,0,,,0,0,0,1:1020002:300;1:450003:2000;,10401004,750,28800
10100501,1,0,30301001:主角等级达到m级,,33,,,0,,,0,0,0,1:1020002:120;1:1000338:2;1:1070101:5;,10401005,1100,28800
10100601,1,0,30301001:主角等级达到m级,,33,,,0,,,0,0,0,1:1020002:980;1:1000339:6;1:1070101:10;,10401006,750,28800
10100701,1,0,30301001:主角等级达到m级,,33,,,0,,,0,0,0,1:1020002:1980;1:1000068:2;1:1070101:20;,10401007,350,28800
10100801,1,0,30301001:主角等级达到m级,,36,,,0,,,0,0,0,1:1020002:680;1:1020102:10;,10401008,450,43200
10100802,1,0,30301001:主角等级达到m级,,36,,,0,,,0,10100801,0,1:1020002:1980;1:1020102:25;,10401009,400,43200
10100803,1,0,30301001:主角等级达到m级,,36,,,0,,,0,10100802,0,1:1020002:6480;1:1020102:70;,10401010,350,43200
10100901,1,0,30301001:主角等级达到m级,,40,,,0,,,0,0,0,1:1020002:300;1:450004:2;1:450005:80;,10401011,1100,43200
10100902,1,0,30301001:主角等级达到m级,,40,,,0,,,0,10100901,0,1:1020002:1280;1:450004:6;1:450005:300;,10401012,900,43200
10100903,1,0,30301001:主角等级达到m级,,40,,,0,,,0,10100902,0,1:1020002:1980;1:450004:10;1:450005:350;,10401013,800,43200
10101001,1,0,30301001:主角等级达到m级,,45,,,0,,,0,0,0,1:1020002:600;1:1000037:2;1:1000039:4;,10401014,1100,43200
10101002,1,0,30301001:主角等级达到m级,,45,,,0,,,0,10101001,0,1:1020002:1980;1:1000037:6;1:1000039:5;,10401015,600,43200
10101003,1,0,30301001:主角等级达到m级,,45,,,0,,,0,10101002,0,1:1020002:3280;1:1000037:10;1:1000039:8;,10401016,400,43200
10101004,1,0,30301001:主角等级达到m级,,45,,,0,,,0,10101003,0,1:1020002:6480;1:1000037:20;1:1000039:15;,10401017,350,43200
10101101,1,0,30301001:主角等级达到m级,,45,,,0,,,0,0,0,1:1020002:980;1:450001:1000000;,10401018,1100,28800
10101201,1,0,11203001:猎头招聘系统开启,,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;1:1000069:6;,10401019,550,43200
10101202,1,0,11203001:猎头招聘系统开启,,1,,,0,,,0,10101201,0,1:1020002:1280;1:1020101:10;1:1000069:6;,10401020,500,43200
10101203,1,0,11203001:猎头招聘系统开启,,1,,,0,,,0,10101202,0,1:1020002:1980;1:1020101:15;,10401021,450,43200
10101204,1,0,11203001:猎头招聘系统开启,,1,,,0,,,0,10101203,0,1:1020002:6480;1:1020101:45;,10401022,450,43200
10101301,1,0,30301001:主角等级达到m级,,55,,,0,,,0,0,0,1:1020002:120;1:80100301:2;,10401023,1000,43200
10101302,1,0,30301001:主角等级达到m级,,55,,,0,,,0,10101301,0,1:1020002:680;1:80100301:5;1:480002:20;,10401024,650,43200
10101303,1,0,30301001:主角等级达到m级,,55,,,0,,,0,10101302,0,1:1020002:1980;1:80100301:15;1:480002:50;,10401025,650,43200
10101304,1,0,30301001:主角等级达到m级,,55,,,0,,,0,10101303,0,1:1020002:3280;1:80100301:20;1:480002:100;,10401026,600,43200
10101401,1,0,30301001:主角等级达到m级,,55,,,0,,,0,0,0,1:1020002:6480;1:8400006:3;1:1020101:30;,10401027,350,28800
10101501,1,0,30301001:主角等级达到m级,,58,,,0,,,0,0,0,1:1020002:300;1:1000211:6;,10401028,950,43200
10101502,1,0,30301001:主角等级达到m级,,58,,,0,,,0,10101501,0,1:1020002:980;1:1000217:2;1:1000211:2;,10401029,700,43200
10101503,1,0,30301001:主角等级达到m级,,58,,,0,,,0,10101502,0,1:1020002:1980;1:1000461:1;1:1000216:1;,10401030,550,43200
10101504,1,0,30301001:主角等级达到m级,,58,,,0,,,0,10101503,0,1:1020002:6480;1:1000461:3;1:1000216:4;,10401031,500,43200
10101601,1,0,30301001:主角等级达到m级,,60,,,0,,,0,0,0,1:1020002:680;1:1020101:5;1:1000069:6;,10401032,550,43200
10101602,1,0,30301001:主角等级达到m级,,60,,,0,,,0,10101601,0,1:1020002:1280;1:1020101:10;1:1000069:6;,10401033,500,43200
10101603,1,0,30301001:主角等级达到m级,,60,,,0,,,0,10101602,0,1:1020002:1980;1:1020101:15;,10401034,450,43200
10101604,1,0,30301001:主角等级达到m级,,60,,,0,,,0,10101603,0,1:1020002:6480;1:1020101:45;,10401035,450,43200
10101701,1,0,30301001:主角等级达到m级,,62,,,0,,,0,0,0,1:1020002:1280;1:1000236:1;,10401036,350,43200
10101702,1,0,30301001:主角等级达到m级,,62,,,0,,,0,10101701,0,1:1020002:3280;1:1000236:2;1:1020102:15;,10401037,400,43200
10101703,1,0,30301001:主角等级达到m级,,62,,,0,,,0,10101702,0,1:1020002:6480;1:1000236:3;1:1020101:20;,10401038,400,43200
10101801,1,0,30301001:主角等级达到m级,,63,,,0,,,0,0,0,1:1020002:6480;1:8400006:3;1:80100301:30;,10401039,400,43200
10101802,1,0,30301001:主角等级达到m级,,63,,,0,,,0,10101801,0,1:1020002:6480;1:8400006:3;1:80100301:30;,10401040,400,43200
10101901,1,0,30301001:主角等级达到m级,,65,,,0,,,0,0,0,1:1020002:680;1:1020102:10;,10401041,500,43200
10101902,1,0,30301001:主角等级达到m级,,65,,,0,,,0,10101901,0,1:1020002:3280;1:1020102:25;1:1000003:30;,10401042,350,43200
10101903,1,0,30301001:主角等级达到m级,,65,,,0,,,0,10101902,0,1:1020002:6480;1:1020102:45;1:1000003:60;,10401043,350,43200
10102001,1,0,30301001:主角等级达到m级,,67,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10401044,500,43200
10102002,1,0,30301001:主角等级达到m级,,67,,,0,,,0,10102001,0,1:1020002:3280;1:1020101:15;1:1000003:30;,10401045,400,43200
10102003,1,0,30301001:主角等级达到m级,,67,,,0,,,0,10102002,0,1:1020002:6480;1:1020101:25;1:1000003:60;,10401046,350,43200
10102101,1,0,30301001:主角等级达到m级,,69,,,0,,,0,0,0,1:1020002:680;1:1020102:10;,10401047,500,43200
10102102,1,0,30301001:主角等级达到m级,,69,,,0,,,0,10102101,0,1:1020002:3280;1:1020102:20;1:30002301:3;,10401048,400,43200
10102103,1,0,30301001:主角等级达到m级,,69,,,0,,,0,10102102,0,1:1020002:6480;1:1020102:40;1:30002301:6;,10401049,400,43200
10102201,1,0,30301001:主角等级达到m级,,71,,,0,,,0,0,0,1:1020002:6480;1:8400007:3;1:1020101:30;,10401050,450,43200
10102202,1,0,30301001:主角等级达到m级,,71,,,0,,,0,10102201,0,1:1020002:6480;1:8400007:3;1:1020101:30;,10401051,450,43200
10102301,1,0,30301001:主角等级达到m级,,73,,,0,,,0,0,0,1:1020002:3280;1:1020102:25;1:1000003:30;,10401052,350,43200
10102302,1,0,30301001:主角等级达到m级,,73,,,0,,,0,10102301,0,1:1020002:3280;1:1020102:25;1:1000003:30;,10401053,350,43200
10102303,1,0,30301001:主角等级达到m级,,73,,,0,,,0,10102302,0,1:1020002:6480;1:1020102:45;1:1000003:60;,10401054,350,43200
10102401,1,0,30301001:主角等级达到m级,,75,,,0,,,0,0,0,1:1020002:680;1:1020102:10;,10401055,500,43200
10102402,1,0,30301001:主角等级达到m级,,75,,,0,,,0,10102401,0,1:1020002:3280;1:1020102:20;1:30002301:3;,10401056,400,43200
10102403,1,0,30301001:主角等级达到m级,,75,,,0,,,0,10102402,0,1:1020002:6480;1:1020102:40;1:30002301:6;,10401057,400,43200
10102501,1,0,30301001:主角等级达到m级,,77,,,0,,,0,0,0,1:1020002:680;1:1000017:20;1:1000067:2;,10401058,600,43200
10102502,1,0,30301001:主角等级达到m级,,77,,,0,,,0,10102501,0,1:1020002:3280;1:1000017:60;1:1000067:8;,10401059,450,43200
10102503,1,0,30301001:主角等级达到m级,,77,,,0,,,0,10102502,0,1:1020002:6480;1:1000017:120;1:1000067:15;,10401060,450,43200
10102601,1,0,30301001:主角等级达到m级,,78,,,0,,,0,0,0,1:1020002:6480;1:8400007:3;1:1020101:30;,10401061,450,43200
10102602,1,0,30301001:主角等级达到m级,,78,,,0,,,0,10102601,0,1:1020002:6480;1:8400007:3;1:1020101:30;,10401062,450,43200
10102603,1,0,30301001:主角等级达到m级,,78,,,0,,,0,10102602,0,1:1020002:6480;1:8400007:3;1:1020101:30;,10401063,450,43200
10102701,1,0,30301001:主角等级达到m级,,79,,,0,,,0,0,0,1:1020002:980;1:1070501:1;1:1070301:3;,10401064,750,43200
10102702,1,0,30301001:主角等级达到m级,,79,,,0,,,0,10102701,0,1:1020002:3280;1:1070501:2;1:1070301:5;,10401065,450,43200
10102703,1,0,30301001:主角等级达到m级,,79,,,0,,,0,10102702,0,1:1020002:6480;1:1070501:4;1:1070301:10;,10401066,450,43200
10102801,1,0,30301001:主角等级达到m级,,80,,,0,,,0,0,0,1:1020002:680;1:1020102:10;,10401067,500,43200
10102802,1,0,30301001:主角等级达到m级,,80,,,0,,,0,10102801,0,1:1020002:3280;1:1020102:20;1:30002301:3;,10401068,400,43200
10102803,1,0,30301001:主角等级达到m级,,80,,,0,,,0,10102802,0,1:1020002:6480;1:1020102:40;1:30002301:6;,10401069,400,43200
10102901,1,0,30301001:主角等级达到m级,,81,,,0,,,0,0,0,1:1020002:680;1:1000017:20;1:1000067:2;,10401070,600,43200
10102902,1,0,30301001:主角等级达到m级,,81,,,0,,,0,10102901,0,1:1020002:3280;1:1000017:60;1:1000067:8;,10401071,450,43200
10102903,1,0,30301001:主角等级达到m级,,81,,,0,,,0,10102902,0,1:1020002:6480;1:1000017:120;1:1000067:15;,10401072,450,43200
10103001,1,0,30301001:主角等级达到m级,,82,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10401073,500,43200
10103002,1,0,30301001:主角等级达到m级,,82,,,0,,,0,10103001,0,1:1020002:3280;1:1020101:15;1:1000003:30;,10401074,400,43200
10103003,1,0,30301001:主角等级达到m级,,82,,,0,,,0,10103002,0,1:1020002:6480;1:1020101:25;1:1000003:60;,10401075,350,43200
10103101,1,0,30301001:主角等级达到m级,,83,,,0,,,0,0,0,1:1020002:980;1:1070501:1;1:1070301:3;,10401076,750,43200
10103102,1,0,30301001:主角等级达到m级,,83,,,0,,,0,10103101,0,1:1020002:3280;1:1070501:2;1:1070301:5;,10401077,450,43200
10103103,1,0,30301001:主角等级达到m级,,83,,,0,,,0,10103102,0,1:1020002:6480;1:1070501:4;1:1070301:10;,10401078,450,43200
10103201,1,0,30301001:主角等级达到m级,,84,,,0,,,0,0,0,1:1020002:680;1:1020102:10;,10401079,500,43200
10103202,1,0,30301001:主角等级达到m级,,84,,,0,,,0,10103201,0,1:1020002:3280;1:1020102:20;1:30002301:3;,10401080,400,43200
10103203,1,0,30301001:主角等级达到m级,,84,,,0,,,0,10103202,0,1:1020002:6480;1:1020102:40;1:30002301:6;,10401081,400,43200
10103301,1,0,30301001:主角等级达到m级,,85,,,0,,,0,0,0,1:1020002:680;1:1000017:20;1:1000067:2;,10401082,600,43200
10103302,1,0,30301001:主角等级达到m级,,85,,,0,,,0,10103301,0,1:1020002:3280;1:1000017:60;1:1000067:8;,10401083,450,43200
10103303,1,0,30301001:主角等级达到m级,,85,,,0,,,0,10103302,0,1:1020002:6480;1:1000017:120;1:1000067:15;,10401084,450,43200
10103401,1,0,30301001:主角等级达到m级,,86,,,0,,,0,0,0,1:1020002:6480;1:8400008:2;1:1020101:30;,10401085,450,43200
10103402,1,0,30301001:主角等级达到m级,,86,,,0,,,0,10103401,0,1:1020002:6480;1:8400008:2;1:1020101:30;,10401086,450,43200
10103403,1,0,30301001:主角等级达到m级,,86,,,0,,,0,10103402,0,1:1020002:6480;1:8400008:2;1:1020101:30;,10401087,450,43200
10103404,1,0,30301001:主角等级达到m级,,86,,,0,,,0,10103403,0,1:1020002:6480;1:8400008:2;1:1020101:30;,10401088,450,43200
10103501,1,0,30301001:主角等级达到m级,,87,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10401089,500,43200
10103502,1,0,30301001:主角等级达到m级,,87,,,0,,,0,10103501,0,1:1020002:3280;1:1020101:15;1:1000003:30;,10401090,400,43200
10103503,1,0,30301001:主角等级达到m级,,87,,,0,,,0,10103502,0,1:1020002:6480;1:1020101:25;1:1000003:60;,10401091,350,43200
10103601,1,0,30301001:主角等级达到m级,,88,,,0,,,0,0,0,1:1020002:980;1:1070501:1;1:1070301:3;,10401092,750,43200
10103602,1,0,30301001:主角等级达到m级,,88,,,0,,,0,10103601,0,1:1020002:3280;1:1070501:2;1:1070301:5;,10401093,450,43200
10103603,1,0,30301001:主角等级达到m级,,88,,,0,,,0,10103602,0,1:1020002:6480;1:1070501:4;1:1070301:10;,10401094,450,43200
10103701,1,0,30301001:主角等级达到m级,,89,,,0,,,0,0,0,1:1020002:680;1:1020102:10;,10401095,500,43200
10103702,1,0,30301001:主角等级达到m级,,89,,,0,,,0,10103701,0,1:1020002:3280;1:1020102:20;1:1000003:30;,10401096,350,43200
10103703,1,0,30301001:主角等级达到m级,,89,,,0,,,0,10103702,0,1:1020002:6480;1:1020102:40;1:1000003:60;,10401097,350,43200
10103801,1,0,30301001:主角等级达到m级,,90,,,0,,,0,0,0,1:1020002:680;1:1000017:20;1:1000067:2;,10401098,600,43200
10103802,1,0,30301001:主角等级达到m级,,90,,,0,,,0,10103801,0,1:1020002:3280;1:1000017:60;1:1000067:8;,10401099,450,43200
10103803,1,0,30301001:主角等级达到m级,,90,,,0,,,0,10103802,0,1:1020002:6480;1:1000017:120;1:1000067:15;,10401100,450,43200
10103901,1,0,30301001:主角等级达到m级,,91,,,0,,,0,0,0,1:1020002:3280;1:1020102:25;1:1000003:30;,10401101,350,43200
10103902,1,0,30301001:主角等级达到m级,,91,,,0,,,0,10103901,0,1:1020002:3280;1:1020102:25;1:1000003:30;,10401102,350,43200
10103903,1,0,30301001:主角等级达到m级,,91,,,0,,,0,10103902,0,1:1020002:6480;1:1020102:45;1:1000003:60;,10401103,350,43200
10104001,1,0,30301001:主角等级达到m级,,92,,,0,,,0,0,0,1:1020002:980;1:1070501:1;1:1070301:3;,10401104,750,43200
10104002,1,0,30301001:主角等级达到m级,,92,,,0,,,0,10104001,0,1:1020002:3280;1:1070501:2;1:1070301:5;,10401105,450,43200
10104003,1,0,30301001:主角等级达到m级,,92,,,0,,,0,10104002,0,1:1020002:6480;1:1070501:4;1:1070301:10;,10401106,450,43200
10104101,1,0,30301001:主角等级达到m级,,93,,,0,,,0,0,0,1:1020002:680;1:1020102:10;,10401107,500,43200
10104102,1,0,30301001:主角等级达到m级,,93,,,0,,,0,10104101,0,1:1020002:3280;1:1020102:20;1:1000003:30;,10401108,350,43200
10104103,1,0,30301001:主角等级达到m级,,93,,,0,,,0,10104102,0,1:1020002:6480;1:1020102:40;1:1000003:60;,10401109,350,43200
10104201,1,0,30301001:主角等级达到m级,,94,,,0,,,0,0,0,1:1020002:6480;1:8400008:3;1:1020101:25;,10401110,450,43200
10104202,1,0,30301001:主角等级达到m级,,94,,,0,,,0,10104201,0,1:1020002:6480;1:8400008:3;1:1020101:25;,10401111,450,43200
10104203,1,0,30301001:主角等级达到m级,,94,,,0,,,0,10104202,0,1:1020002:6480;1:8400008:3;1:1020101:25;,10401112,450,43200
10104204,1,0,30301001:主角等级达到m级,,94,,,0,,,0,10104203,0,1:1020002:6480;1:8400008:3;1:1020101:25;,10401113,450,43200
10204301,1,0,20100001:主线完成指定关卡,10100105,1,,,0,,,0,0,0,2:2021062:30;1:3010302:10;,10402001,1800,28800
10204401,1,0,20100001:主线完成指定关卡,10100610,1,,,0,,,0,0,0,1:1020002:120;1:1000005:2;,10402002,1500,28800
10204501,1,0,20100001:主线完成指定关卡,10100910,1,,,0,,,0,0,0,1:1020002:680;1:1020102:10;,10402003,500,43200
10204502,1,0,20100001:主线完成指定关卡,10100910,1,,,0,,,0,10204501,0,1:1020002:1980;1:1020102:25;,10402004,450,43200
10204503,1,0,20100001:主线完成指定关卡,10100910,1,,,0,,,0,10204502,0,1:1020002:6480;1:1020102:70;,10402005,400,43200
10204601,1,0,20100001:主线完成指定关卡,10101410,1,,,0,,,0,0,0,1:1020002:300;1:1020102:10;,10402006,950,43200
10204602,1,0,20100001:主线完成指定关卡,10101410,1,,,0,,,0,10204601,0,1:1020002:980;1:1020102:15;,10402007,500,43200
10204603,1,0,20100001:主线完成指定关卡,10101410,1,,,0,,,0,10204602,0,1:1020002:1980;1:1020102:25;,10402008,450,43200
10204604,1,0,20100001:主线完成指定关卡,10101410,1,,,0,,,0,10204603,0,1:1020002:6480;1:1020102:70;,10402009,400,43200
10204701,1,0,20100001:主线完成指定关卡,10101910,1,,,0,,,0,0,0,1:1020002:680;1:1020102:10;,10402010,500,28800
10204801,1,0,20100001:主线完成指定关卡,10102410,1,,,0,,,0,0,0,1:1020002:680;1:1020102:10;,10402011,500,28800
10204901,1,0,20100001:主线完成指定关卡,10102910,1,,,0,,,0,0,0,1:1020002:680;1:1020102:10;,10402012,500,43200
10204902,1,0,20100001:主线完成指定关卡,10102910,1,,,0,,,0,10204901,0,1:1020002:1980;1:1020102:25;,10402013,450,43200
10204903,1,0,20100001:主线完成指定关卡,10102910,1,,,0,,,0,10204902,0,1:1020002:6480;1:1020102:70;,10402014,400,43200
10205001,1,0,20100001:主线完成指定关卡,10103410,1,,,0,,,0,0,0,1:1020002:1980;1:1020102:25;,10402015,450,28800
10205101,1,0,20100001:主线完成指定关卡,10103910,1,,,0,,,0,0,0,1:1020002:1980;1:1020102:15;1:30002301:2;,10402016,450,28800
10205201,1,0,20100001:主线完成指定关卡,10104110,1,,,0,,,0,0,0,1:1020002:980;1:1020102:10;1:30002301:1;,10402017,500,43200
10205202,1,0,20100001:主线完成指定关卡,10104110,1,,,0,,,0,10205201,0,1:1020002:3280;1:1020102:20;1:30002301:3;,10402018,400,43200
10205203,1,0,20100001:主线完成指定关卡,10104110,1,,,0,,,0,10205202,0,1:1020002:6480;1:1020102:40;1:30002301:6;,10402019,400,43200
10205301,1,0,20100001:主线完成指定关卡,10104510,1,,,0,,,0,0,0,1:1020002:1980;1:1020102:25;,10402020,450,28800
10205401,1,0,20100001:主线完成指定关卡,10104910,1,,,0,,,0,0,0,1:1020002:1980;1:1020102:15;1:30002301:2;,10402021,450,28800
10205501,1,0,20100001:主线完成指定关卡,10105310,1,,,0,,,0,0,0,1:1020002:980;1:1020102:10;1:30002301:1;,10402022,500,43200
10205502,1,0,20100001:主线完成指定关卡,10105310,1,,,0,,,0,10205501,0,1:1020002:3280;1:1020102:20;1:30002301:3;,10402023,400,43200
10205503,1,0,20100001:主线完成指定关卡,10105310,1,,,0,,,0,10205502,0,1:1020002:6480;1:1020102:40;1:30002301:6;,10402024,400,43200
10205601,1,0,20100001:主线完成指定关卡,10105710,1,,,0,,,0,0,0,1:1020002:1980;1:1020102:25;,10402025,450,28800
10205701,1,0,20100001:主线完成指定关卡,10106110,1,,,0,,,0,0,0,1:1020002:1980;1:1020102:15;1:30002301:2;,10402026,450,28800
10205801,1,0,20100001:主线完成指定关卡,10106510,1,,,0,,,0,0,0,1:1020002:980;1:1020102:10;1:30002301:1;,10402027,500,43200
10205802,1,0,20100001:主线完成指定关卡,10106510,1,,,0,,,0,10205801,0,1:1020002:3280;1:1020102:20;1:30002301:3;,10402028,400,43200
10205803,1,0,20100001:主线完成指定关卡,10106510,1,,,0,,,0,10205802,0,1:1020002:6480;1:1020102:40;1:30002301:6;,10402029,400,43200
10205901,1,0,20100001:主线完成指定关卡,10106910,1,,,0,,,0,0,0,1:1020002:1980;1:1020102:25;,10402030,450,28800
10206001,1,0,20100001:主线完成指定关卡,10107410,1,,,0,,,0,0,0,1:1020002:1980;1:1020102:15;1:30002301:2;,10402031,450,28800
10206101,1,0,20100001:主线完成指定关卡,10107910,1,,,0,,,0,0,0,1:1020002:980;1:1020102:10;1:30002301:1;,10402032,500,43200
10206102,1,0,20100001:主线完成指定关卡,10107910,1,,,0,,,0,10206101,0,1:1020002:3280;1:1020102:20;1:30002301:3;,10402033,400,43200
10206103,1,0,20100001:主线完成指定关卡,10107910,1,,,0,,,0,10206102,0,1:1020002:6480;1:1020102:40;1:30002301:6;,10402034,400,43200
10206201,1,0,20100001:主线完成指定关卡,10108410,1,,,0,,,0,0,0,1:1020002:1980;1:1020102:25;,10402035,450,28800
10206301,1,0,20100001:主线完成指定关卡,10108910,1,,,0,,,0,0,0,1:1020002:1980;1:1020102:15;1:30002301:2;,10402036,450,28800
10206401,1,0,20100001:主线完成指定关卡,10109410,1,,,0,,,0,0,0,1:1020002:980;1:1020102:10;1:30002301:1;,10402037,500,43200
10206402,1,0,20100001:主线完成指定关卡,10109410,1,,,0,,,0,10206401,0,1:1020002:3280;1:1020102:20;1:30002301:3;,10402038,400,43200
10206403,1,0,20100001:主线完成指定关卡,10109410,1,,,0,,,0,10206402,0,1:1020002:6480;1:1020102:40;1:30002301:6;,10402039,400,43200
10206501,1,0,20100001:主线完成指定关卡,10109910,1,,,0,,,0,0,0,1:1020002:1980;1:1020102:25;,10402040,450,28800
10306601,1,0,20100001:主线完成指定关卡,20100310,1,,,0,,,0,0,0,1:1020002:300;1:1020101:5;,10403001,900,28800
10306701,1,0,20100001:主线完成指定关卡,20100610,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403002,500,28800
10306801,1,0,20100001:主线完成指定关卡,20100910,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403003,500,43200
10306802,1,0,20100001:主线完成指定关卡,20100910,1,,,0,,,0,10306801,0,1:1020002:1980;1:1020101:15;,10403004,500,43200
10306803,1,0,20100001:主线完成指定关卡,20100910,1,,,0,,,0,10306802,0,1:1020002:6480;1:1020101:45;,10403005,450,43200
10306901,1,0,20100001:主线完成指定关卡,20101210,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403006,500,28800
10307001,1,0,20100001:主线完成指定关卡,20101510,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403007,500,28800
10307101,1,0,20100001:主线完成指定关卡,20101810,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403008,500,43200
10307102,1,0,20100001:主线完成指定关卡,20101810,1,,,0,,,0,10307101,0,1:1020002:1980;1:1020101:15;,10403009,500,43200
10307103,1,0,20100001:主线完成指定关卡,20101810,1,,,0,,,0,10307102,0,1:1020002:6480;1:1020101:45;,10403010,450,43200
10307201,1,0,20100001:主线完成指定关卡,20102010,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403011,500,28800
10307301,1,0,20100001:主线完成指定关卡,20102410,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403012,500,28800
10307401,1,0,20100001:主线完成指定关卡,20102710,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403013,500,43200
10307402,1,0,20100001:主线完成指定关卡,20102710,1,,,0,,,0,10307401,0,1:1020002:1980;1:1020101:15;,10403014,500,43200
10307403,1,0,20100001:主线完成指定关卡,20102710,1,,,0,,,0,10307402,0,1:1020002:6480;1:1020101:45;,10403015,450,43200
10307501,1,0,20100001:主线完成指定关卡,20103310,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403016,500,28800
10307601,1,0,20100001:主线完成指定关卡,20103610,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403017,500,43200
10307602,1,0,20100001:主线完成指定关卡,20103610,1,,,0,,,0,10307601,0,1:1020002:1980;1:1020101:15;,10403018,500,43200
10307603,1,0,20100001:主线完成指定关卡,20103610,1,,,0,,,0,10307602,0,1:1020002:6480;1:1020101:45;,10403019,450,43200
10307701,1,0,20100001:主线完成指定关卡,20103910,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403020,500,28800
10307801,1,0,20100001:主线完成指定关卡,20104210,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403021,500,28800
10307901,1,0,20100001:主线完成指定关卡,20104510,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403022,500,43200
10307902,1,0,20100001:主线完成指定关卡,20104510,1,,,0,,,0,10307901,0,1:1020002:1980;1:1020101:15;,10403023,500,43200
10307903,1,0,20100001:主线完成指定关卡,20104510,1,,,0,,,0,10307902,0,1:1020002:6480;1:1020101:45;,10403024,450,43200
10308001,1,0,20100001:主线完成指定关卡,20104810,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403025,500,28800
10308101,1,0,20100001:主线完成指定关卡,20105110,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403026,500,28800
10308201,1,0,20100001:主线完成指定关卡,20105410,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403027,500,43200
10308202,1,0,20100001:主线完成指定关卡,20105410,1,,,0,,,0,10308201,0,1:1020002:1980;1:1020101:15;,10403028,500,43200
10308203,1,0,20100001:主线完成指定关卡,20105410,1,,,0,,,0,10308202,0,1:1020002:6480;1:1020101:45;,10403029,450,43200
10308301,1,0,20100001:主线完成指定关卡,20105710,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403030,500,28800
10308401,1,0,20100001:主线完成指定关卡,20106010,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403031,500,28800
10308501,1,0,20100001:主线完成指定关卡,20106310,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403032,500,43200
10308502,1,0,20100001:主线完成指定关卡,20106310,1,,,0,,,0,10308501,0,1:1020002:1980;1:1020101:15;,10403033,500,43200
10308503,1,0,20100001:主线完成指定关卡,20106310,1,,,0,,,0,10308502,0,1:1020002:6480;1:1020101:45;,10403034,450,43200
10308601,1,0,20100001:主线完成指定关卡,20106610,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403035,500,28800
10308701,1,0,20100001:主线完成指定关卡,20106910,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403036,500,28800
10308801,1,0,20100001:主线完成指定关卡,20107210,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403037,500,43200
10308802,1,0,20100001:主线完成指定关卡,20107210,1,,,0,,,0,10308801,0,1:1020002:1980;1:1020101:15;,10403038,500,43200
10308803,1,0,20100001:主线完成指定关卡,20107210,1,,,0,,,0,10308802,0,1:1020002:6480;1:1020101:45;,10403039,450,43200
10308901,1,0,20100001:主线完成指定关卡,20107510,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403040,500,28800
10309001,1,0,20100001:主线完成指定关卡,20107810,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403041,500,28800
10309101,1,0,20100001:主线完成指定关卡,20108110,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403042,500,43200
10309102,1,0,20100001:主线完成指定关卡,20108110,1,,,0,,,0,10309101,0,1:1020002:1980;1:1020101:15;,10403043,500,43200
10309103,1,0,20100001:主线完成指定关卡,20108110,1,,,0,,,0,10309102,0,1:1020002:6480;1:1020101:45;,10403044,450,43200
10309201,1,0,20100001:主线完成指定关卡,20108410,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403045,500,28800
10309301,1,0,20100001:主线完成指定关卡,20108710,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403046,500,28800
10309401,1,0,20100001:主线完成指定关卡,20109010,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403047,500,43200
10309402,1,0,20100001:主线完成指定关卡,20109010,1,,,0,,,0,10309401,0,1:1020002:1980;1:1020101:15;,10403048,500,43200
10309403,1,0,20100001:主线完成指定关卡,20109010,1,,,0,,,0,10309402,0,1:1020002:6480;1:1020101:45;,10403049,450,43200
10309501,1,0,20100001:主线完成指定关卡,20109310,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403050,500,28800
10309601,1,0,20100001:主线完成指定关卡,20109610,1,,,0,,,0,0,0,1:1020002:1980;1:1020101:15;,10403051,500,28800
10309701,1,0,20100001:主线完成指定关卡,20109910,1,,,0,,,0,0,0,1:1020002:680;1:1020101:5;,10403052,500,43200
10309702,1,0,20100001:主线完成指定关卡,20109910,1,,,0,,,0,10309701,0,1:1020002:1980;1:1020101:15;,10403053,500,43200
10309703,1,0,20100001:主线完成指定关卡,20109910,1,,,0,,,0,10309702,0,1:1020002:6480;1:1020101:45;,10403054,450,43200
10409801,1,0,50101001:八极大厦 - 通关至第n关,10000100,1,,,0,,,0,0,0,1:1020002:680;1:450001:300000;1:450003:888;,10404001,700,28800
10409901,1,0,50101001:八极大厦 - 通关至第n关,10000200,1,,,0,,,0,0,0,1:1020002:980;1:450001:400000;1:450003:1588;,10404002,700,28800
10410001,1,0,50101001:八极大厦 - 通关至第n关,10000300,1,,,0,,,0,0,0,1:1020002:980;1:450001:200000;1:450003:3588;,10404003,700,28800
10410101,1,0,50101001:八极大厦 - 通关至第n关,10000400,1,,,0,,,0,0,0,1:1020002:980;1:450001:450000;1:450003:1288;,10404004,700,28800
10410201,1,0,50101001:八极大厦 - 通关至第n关,10000600,1,,,0,,,0,0,0,1:1020002:1280;1:450004:15;1:450005:80;,10404006,850,28800
10410301,1,0,50101001:八极大厦 - 通关至第n关,10000800,1,,,0,,,0,0,0,1:1020002:3280;1:450002:4;1:450003:8000;,10404008,550,28800
10410401,1,0,50101001:八极大厦 - 通关至第n关,10001000,1,,,0,,,0,0,0,1:1020002:1280;1:450004:15;1:450005:80;,10404010,850,28800
10410501,1,0,50101001:八极大厦 - 通关至第n关,10001200,1,,,0,,,0,0,0,1:1020002:3280;1:450002:4;1:450003:8000;,10404012,550,28800
10410601,1,0,50101001:八极大厦 - 通关至第n关,10001400,1,,,0,,,0,0,0,1:1020002:1280;1:450004:15;1:450005:80;,10404014,850,28800
10410701,1,0,50101001:八极大厦 - 通关至第n关,10001600,1,,,0,,,0,0,0,1:1020002:3280;1:450002:4;1:450003:8000;,10404016,550,28800
10410801,1,0,50101001:八极大厦 - 通关至第n关,10001800,1,,,0,,,0,0,0,1:1020002:1280;1:450004:15;1:450005:80;,10404018,850,28800
10410901,1,0,50101001:八极大厦 - 通关至第n关,10002000,1,,,0,,,0,0,0,1:1020002:3280;1:450002:4;1:450003:8000;,10404020,550,28800
10411001,1,0,50101001:八极大厦 - 通关至第n关,10002200,1,,,0,,,0,0,0,1:1020002:1280;1:450004:15;1:450005:80;,10404022,850,28800
10411101,1,0,50101001:八极大厦 - 通关至第n关,10002400,1,,,0,,,0,0,0,1:1020002:3280;1:450002:4;1:450003:8000;,10404024,550,28800
10411201,1,0,50101001:八极大厦 - 通关至第n关,10002600,1,,,0,,,0,0,0,1:1020002:1280;1:450004:15;1:450005:80;,10404026,850,28800
10411301,1,0,50101001:八极大厦 - 通关至第n关,10002800,1,,,0,,,0,0,0,1:1020002:3280;1:450002:4;1:450003:8000;,10404028,550,28800
10411401,1,0,50101001:八极大厦 - 通关至第n关,10003000,1,,,0,,,0,0,0,1:1020002:1280;1:450004:15;1:450005:80;,10404030,850,28800
10411501,1,0,50101001:八极大厦 - 通关至第n关,10003200,1,,,0,,,0,0,0,1:1020002:3280;1:450002:4;1:450003:8000;,10404032,550,28800
10411601,1,0,50101001:八极大厦 - 通关至第n关,10003400,1,,,0,,,0,0,0,1:1020002:1280;1:450004:15;1:450005:80;,10404034,850,28800
10411701,1,0,50101001:八极大厦 - 通关至第n关,10003600,1,,,0,,,0,0,0,1:1020002:3280;1:450002:4;1:450003:8000;,10404036,550,28800
10411801,1,0,50101001:八极大厦 - 通关至第n关,10003800,1,,,0,,,0,0,0,1:1020002:1280;1:450004:15;1:450005:80;,10404038,850,28800
10411901,1,0,50101001:八极大厦 - 通关至第n关,10004000,1,,,0,,,0,0,0,1:1020002:3280;1:450002:4;1:450003:8000;,10404040,550,28800
10612001,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,30,,,0,,,0,0,0,1:1020002:300;1:1000037:2;1:1030107:100;,10406001,1000,28800
10612101,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,60,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406002,650,28800
10612201,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,90,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406003,650,43200
10612202,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,90,,,0,,,0,10612201,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406004,650,43200
10612203,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,90,,,0,,,0,10612202,0,1:1020002:6480;1:1000037:20;1:1030107:1200;,10406005,550,43200
10612301,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,120,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406006,650,28800
10612401,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,150,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406007,650,28800
10612501,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,180,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406008,650,43200
10612502,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,180,,,0,,,0,10612501,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406009,650,43200
10612503,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,180,,,0,,,0,10612502,0,1:1020002:6480;1:1000037:20;1:1030107:1200;,10406010,550,43200
10612601,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,210,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406011,650,28800
10612701,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,240,,,0,,,0,0,0,1:1020002:1980;1:3004201:1;1:1000037:3;,10406012,450,28800
10612801,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,270,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406013,650,43200
10612802,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,270,,,0,,,0,10612801,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406014,650,43200
10612803,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,270,,,0,,,0,10612802,0,1:1020002:6480;1:1000037:20;1:1030107:1200;,10406015,550,43200
10612901,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,300,,,0,,,0,0,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406016,650,28800
10613001,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,330,,,0,,,0,0,0,1:1020002:1980;1:3004201:1;1:1000037:3;,10406017,450,28800
10613101,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,360,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406018,650,43200
10613102,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,360,,,0,,,0,10613101,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406019,650,43200
10613103,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,360,,,0,,,0,10613102,0,1:1020002:6480;1:1000037:20;1:1030107:1200;,10406020,550,43200
10613201,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,390,,,0,,,0,0,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406021,650,28800
10613301,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,420,,,0,,,0,0,0,1:1020002:1980;1:3004201:1;1:1000037:3;,10406022,450,28800
10613401,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,450,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406023,650,43200
10613402,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,450,,,0,,,0,10613401,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406024,650,43200
10613403,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,450,,,0,,,0,10613402,0,1:1020002:6480;1:1000037:20;1:1030107:1200;,10406025,550,43200
10613501,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,480,,,0,,,0,0,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406026,650,28800
10613601,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,510,,,0,,,0,0,0,1:1020002:1980;1:3004201:1;1:1000037:3;,10406027,450,28800
10613701,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,540,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406028,650,43200
10613702,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,540,,,0,,,0,10613701,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406029,650,43200
10613703,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,540,,,0,,,0,10613702,0,1:1020002:6480;1:1000037:20;1:1030107:1200;,10406030,550,43200
10613801,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,570,,,0,,,0,0,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406031,650,28800
10613901,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,600,,,0,,,0,0,0,1:1020002:1980;1:3004201:1;1:1000037:3;,10406032,450,28800
10614001,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,630,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406033,650,43200
10614002,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,630,,,0,,,0,10614001,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406034,650,43200
10614003,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,630,,,0,,,0,10614002,0,1:1020002:6480;1:1000037:20;1:1030107:1200;,10406035,550,43200
10614101,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,660,,,0,,,0,0,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406036,650,28800
10614201,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,690,,,0,,,0,0,0,1:1020002:1980;1:3004201:1;1:1000037:3;,10406037,450,28800
10614301,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,720,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406038,650,43200
10614302,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,720,,,0,,,0,10614301,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406039,650,43200
10614303,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,720,,,0,,,0,10614302,0,1:1020002:6480;1:1000037:20;1:1030107:1200;,10406040,550,43200
10614401,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,750,,,0,,,0,0,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406041,650,28800
10614501,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,780,,,0,,,0,0,0,1:1020002:1980;1:3004201:1;1:1000037:3;,10406042,450,28800
10614601,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,810,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406043,650,43200
10614602,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,810,,,0,,,0,10614601,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406044,650,43200
10614603,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,810,,,0,,,0,10614602,0,1:1020002:6480;1:1000037:20;1:1030107:1200;,10406045,550,43200
10614701,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,840,,,0,,,0,0,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406046,650,28800
10614801,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,870,,,0,,,0,0,0,1:1020002:1980;1:3004201:1;1:1000037:3;,10406047,450,28800
10614901,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,900,,,0,,,0,0,0,1:1020002:980;1:1000037:4;1:1030107:300;,10406048,650,43200
10614902,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,900,,,0,,,0,10614901,0,1:1020002:1980;1:1000037:8;1:1030107:500;,10406049,650,43200
10614903,1,0,50103002:[累计] - 四神幻境 - 通关关卡数量,,900,,,0,,,0,10614902,0,1:1020002:6480;1:1000037:20;1:1030107:1200;,10406050,550,43200
10715001,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,100,,,0,,,0,0,0,1:1020002:300;1:80100301:5;,10407001,950,28800
10715101,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,250,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407002,650,43200
10715102,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,250,,,0,,,0,10715101,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407003,500,43200
10715103,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,250,,,0,,,0,10715102,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407004,500,43200
10715201,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,400,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407005,550,28800
10715301,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,550,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407006,650,43200
10715302,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,550,,,0,,,0,10715301,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407007,500,43200
10715303,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,550,,,0,,,0,10715302,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407008,500,43200
10715401,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,700,,,0,,,0,0,0,1:1020002:300;1:80100301:5;,10407009,950,28800
10715501,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,850,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407010,650,43200
10715502,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,850,,,0,,,0,10715501,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407011,500,43200
10715503,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,850,,,0,,,0,10715502,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407012,500,43200
10715601,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,1000,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407013,550,28800
10715701,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,1150,,,0,,,0,0,0,1:1020002:300;1:80100301:5;,10407014,950,28800
10715801,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,1300,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407015,650,43200
10715802,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,1300,,,0,,,0,10715801,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407016,500,43200
10715803,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,1300,,,0,,,0,10715802,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407017,500,43200
10715901,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,1450,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407018,550,28800
10716001,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,1600,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407019,550,28800
10716101,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,1750,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407020,650,43200
10716102,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,1750,,,0,,,0,10716101,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407021,500,43200
10716103,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,1750,,,0,,,0,10716102,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407022,500,43200
10716201,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,1900,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407023,550,28800
10716301,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,2050,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407024,550,28800
10716401,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,2200,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407025,650,43200
10716402,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,2200,,,0,,,0,10716401,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407026,500,43200
10716403,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,2200,,,0,,,0,10716402,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407027,500,43200
10716501,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,2350,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407028,550,28800
10716601,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,2500,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407029,550,28800
10716701,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,2650,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407030,650,43200
10716702,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,2650,,,0,,,0,10716701,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407031,500,43200
10716703,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,2650,,,0,,,0,10716702,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407032,500,43200
10716801,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,2800,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407033,550,28800
10716901,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,2950,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407034,550,28800
10717001,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,3100,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407035,650,43200
10717002,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,3100,,,0,,,0,10717001,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407036,500,43200
10717003,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,3100,,,0,,,0,10717002,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407037,500,43200
10717101,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,3250,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407038,550,28800
10717201,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,3400,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407039,550,28800
10717301,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,3550,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407040,650,43200
10717302,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,3550,,,0,,,0,10717301,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407041,500,43200
10717303,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,3550,,,0,,,0,10717302,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407042,500,43200
10717401,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,3700,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407043,550,28800
10717501,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,3850,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407044,550,28800
10717601,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,4000,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407045,650,43200
10717602,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,4000,,,0,,,0,10717601,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407046,500,43200
10717603,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,4000,,,0,,,0,10717602,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407047,500,43200
10717701,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,4150,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407048,550,28800
10717801,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,4300,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407049,550,28800
10717901,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,4450,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407050,650,43200
10717902,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,4450,,,0,,,0,10717901,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407051,500,43200
10717903,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,4450,,,0,,,0,10717902,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407052,500,43200
10718001,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,4600,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407053,550,28800
10718101,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,4750,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407054,550,28800
10718201,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,4900,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407055,650,43200
10718202,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,4900,,,0,,,0,10718201,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407056,500,43200
10718203,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,4900,,,0,,,0,10718202,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407057,500,43200
10718301,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,5050,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407058,550,28800
10718401,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,5200,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407059,550,28800
10718501,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,5350,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407060,650,43200
10718502,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,5350,,,0,,,0,10718501,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407061,500,43200
10718503,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,5350,,,0,,,0,10718502,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407062,500,43200
10718601,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,5500,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407063,550,28800
10718701,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,5650,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407064,550,28800
10718801,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,5800,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407065,650,43200
10718802,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,5800,,,0,,,0,10718801,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407066,500,43200
10718803,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,5800,,,0,,,0,10718802,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407067,500,43200
10718901,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,5950,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407068,550,28800
10719001,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,6100,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407069,550,28800
10719101,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,6250,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407070,650,43200
10719102,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,6250,,,0,,,0,10719101,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407071,500,43200
10719103,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,6250,,,0,,,0,10719102,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407072,500,43200
10719201,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,6400,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407073,550,28800
10719301,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,6550,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407074,550,28800
10719401,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,6700,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407075,650,43200
10719402,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,6700,,,0,,,0,10719401,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407076,500,43200
10719403,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,6700,,,0,,,0,10719402,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407077,500,43200
10719501,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,6850,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407078,550,28800
10719601,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,7000,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407079,550,28800
10719701,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,7150,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407080,650,43200
10719702,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,7150,,,0,,,0,10719701,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407081,500,43200
10719703,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,7150,,,0,,,0,10719702,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407082,500,43200
10719801,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,7300,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407083,550,28800
10719901,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,7450,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407084,550,28800
10720001,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,7600,,,0,,,0,0,0,1:1020002:680;1:80100301:5;1:480002:20;,10407085,650,43200
10720002,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,7600,,,0,,,0,10720001,0,1:1020002:3280;1:80100301:20;1:480002:60;,10407086,500,43200
10720003,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,7600,,,0,,,0,10720002,0,1:1020002:6480;1:80100301:40;1:480002:100;,10407087,500,43200
10720101,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,7750,,,0,,,0,0,0,1:1020002:1980;1:80100301:15;1:480002:20;,10407088,550,28800
10720201,1,0,50116001:[终极之战] - [累计] - 通过总关卡数,,7900,,,0,,,0,0,0,1:1020002:3280;1:1000345:1;1:80100301:20;,10407089,550,28800
