所属系统,刮球一次消耗道具,刮球一次获得奖励,重新刮一张卡消耗 （与次数挂钩）,重新刮一张次数,聚宝盆开奖时间 （每天几点）,活动2阶段 纸星兑换持续时间,全服中奖记录显示上限,聚宝盆初始钻石数目,钻石上限,奖池累积奖励系数,单张刮刮卡中奖次数概率,保底中特等奖的刮刮卡张数
SystemID,CostItem,RewardItem,RefreshCostItem,RefreshTimes,OpenPoolDays,DrawCloseDay,WorldRewardsRecord,DiamondInit,DiamondLimit,Coefficient,RewardTimesRatio,MinimumGuarantee
,,,,,,,,,,,,
int,class:SItem[int:type;int:id;int:count],class:SItem[int:type;int:id;int:count],vector:SItem[int:type;int:id;int:count],int,vector:int[],int,int,int,int,int,vector:int[],int
90305,1:1000318:1,1:1000319:20,1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50,10,18,126000,20,10000,50000,260,10;45;45,60
