_ID,玩家初始物品,队伍上阵格斗家数量,队伍援护格斗家数量,格斗家排行榜默认显示,初始格斗家头像,初始形象,初始头像框,每点体力恢复时长,初始英雄列表,伤害浮动 数值(万分比),玩家初始天梯段位,玩家初始ELO积分,禁人阶段时长,选人阶段时长,连击加成,回合上限_天梯竞技场,PVP操作时长,连击hit数清零时长,改名免费次数,改名费用,英文名字长度最大值,聊天_文字长度,聊天_发言间隔,聊天_重复发言间隔,格斗家升级消耗shopID,背包上限,邮件有效时间(天),斗魂数量上限,黑名单上限,统一系统模块重置时间-小时,统一系统模块重置时间-分钟,初始阵容(用于pve),攻击系数,防御系数1,防御系数2,PVE最大能量条值,PVP最大能量条值,PVE最大能量豆数量,PVP最大能量豆数量,援护位按位置的初始cd,邮件未读存储上限,邮件已读存储上限,格斗家击杀目标获得能量,格斗家被击杀己方获得能量,护盾与生命的基础转化系数,护盾衰减系数,资源副本-门票道具,资源副本-门票每日自动获取数量,资源副本-门票数量上限,资源副本-经验本-每日多倍奖励次数上限,资源副本-金币本-门票消耗数量,资源副本-初始门票数量,挑战任务每日抽取数量,竞技场_场景库,受击获取能量,聊天_世界频道开启等级,公平竞技_Ban人缓冲时间,公平竞技_Pick人缓冲时间,竞技场_排名上限,战斗强制结束时间_秒,回合增伤,新手引导之输入玩家昵称的任务id,格斗家礼装cost上限值,礼装强化等级上限,礼装星级上限,最大队伍数量,竞技场_每日免费挑战次数,竞技场_每日付费挑战,竞技场_初始积分,竞技场_换一批回复时长（秒）,竞技场_换一批储存上限,竞技场_战报保存条数,竞技场_挑战胜利奖励点数,竞技场_挑战失败奖励点数,竞技场_点数节点1,竞技场_点数奖励1,竞技场_点数节点2,竞技场_点数奖励2,竞技场_点数节点3,竞技场_点数奖励3,竞技场_定制积分规则(rank:win:lose）,竞技场_定制点数规则(rank:win:lose）,R核心引擎万能芯片兑换目标芯片比率,SR核心引擎万能芯片兑换目标芯片比率,SSR核心引擎万能芯片兑换目标芯片比率,R核心引擎万能芯片兑换SR级别比率,SR核心引擎万能芯片兑换SSR级别比率,礼装强化经验折损比率,主界面排行榜数据显示等级要求,PVP结算主城形象随机格斗家,资源购买_日常商店ID,资源购买_体力商品ID,场景破坏条件1_指定技能造成损血大于等于总血量的n%,场景破坏条件2_当前受击者的血量小于等于n%,波次结束时清除的count类型,格斗家死亡时清除的count类型,出手前移除的count类型,格斗家初始技能等级,格斗家等级上限取参（当前玩家等级+N）,格斗家等级上限值,每日发送弹幕最大上限,穿盾伤害buff类型,格斗家初始技能解锁条件（格斗家星级）,元气物品的id,体力物品的id,金币对的物品id,勾玉(钻石)对应的物品id,冠军杯_货币ID,公平竞技_货币ID,竞技场_货币ID,冠军徽章对应的物品id,组队_队伍房间显示数量上限,组队_队伍房间存在时长（s）,组队_队伍喇叭CD时长（s）,组队_邀请提示存在时间（s）,组队_布阵准备时间（s）,组队_组队loading时长上限（s）,组队_2人队伍,组队_3人队伍,组队_连续邀请缓冲时间（s）,编队_编组名称长度,冠军杯_玩法开放时间,冠军杯_关闭结算点时长,冠军杯_首n场匹配机器人,冠军杯_前n个赛季本服,冠军杯_首赛季计数参考天数,冠军杯_BP禁用时长,冠军杯_BP选用时长,冠军杯_布阵准备时间,冠军杯_布阵缓冲时间,冠军杯_战斗Loading时间上限,冠军杯_战斗回合时长,冠军杯_战斗疲劳规则,冠军杯_段位保护累计上限,冠军杯_战报累计上限,冠军杯_本服排名显示上限,冠军杯_本服排名未显示阈值,冠军杯_世界排名显示上限,冠军杯_世界排名未显示阈值,冠军杯_段位排名显示规则,冠军杯_机器人段位序列浮动值,组队_布阵完成缓冲时间（s）,冠军杯_BP禁用每轮结束后动画时长,冠军杯_BP选人每轮结束后动画时长,冠军杯_Ban人缓冲时间,冠军杯_Pick人缓冲时间,冠军杯_Ban机器人自动禁人时间,冠军杯_Pick机器人自动选人时间,好友_友情点对应货币,好友_同服好友上限,好友_跨服好友上限,好友_最近联系上限,好友_最近组队上限,好友_领取友情点次数,好友_离线暂存消息数,好友_好友推荐规则,好友_申请列表上限,公平竞技_活动开启时间,公平竞技_奖励邮件ID,公平竞技_门票规则,公平竞技_匹配规则,公平竞技_首次机器人场数,公平竞技_机器人时长,公平竞技_编队准备时间,公平竞技_编队缓冲时间,公平竞技_Loading等待时间,公平竞技_场景库,公平竞技_回合时长,公平竞技_榜单显示数,公平竞技_榜单刷新间隔,公平竞技_战报条数,公平竞技_单通切换时间阈值,冰盾属性减一时受击目标加的countid,天赋页每周重置免费次数,天赋页花钻重置价格,公平竞技_机器人自动禁人时间,公平竞技_机器人自动选人时间,公平竞技_机器人自动选人单职业上限,公平竞技_胜利次数触发轮次结束,公平竞技_失败次数触发轮次结束,通用_机器人匹配时间区间,广告图弹出内置cd(min),每日邮件发送时间,资源副本-酒吧本-每日多倍奖励次数上限,好友_招呼语,任务-战队经验,任务-活跃度,任务-成长值,冠军杯_开启所需格斗家数量,一键跳过新手引导,愤怒追击触发后获得的BUFF,邮件活动相关ID,冠军杯奖励天数（赛季开始当天算第一天),协会名长度限制,协会宣言长度,审核等级列表（废弃）,创建协会消耗/道具ID,协会改名消耗/道具ID,协会转让邮件id,申请过期时间（分钟),邀请过期时间（分钟),离开协会再次加入所需冷去时间（单位：分）,协会最大的申请人数,玩家最大的申请协会数量,玩家最大的邀请协会数量,发送招募消息冷却时间(秒),副会长最大人数,协会最大日志数量,协会日志过期时间（分钟),会长弹劾时间（分钟),退出协会时，自动发活跃奖的邮件id,会长变更邮件id,协会推荐列表中最大的申请人数,协会职位名称,协会_协会商店ID,命运卡洗练材料对应金币消耗和命运属性概率（万分比）,1v1切磋_格斗家数量限制,1v1切磋_邀请消息时长,1v1切磋_场景组,捐赠设置(每日捐献次数，每次捐献次数，最大存在时间（h）),捐赠物类型(索引item表的type),单个捐赠奖励(道具，数量),捐赠开启等级(协会等级),首充奖励,任务奖励的格斗家直接上阵,派遣栏位开启等级,大成功奖励加成（1.5倍就是50）,公式中的品阶等级,推荐格斗家加成（百分比）,普通格斗家加成（百分比）,派遣时长（h）,单个经历奖励,章节轮次奖励邮件id,击破奖励邮件id,GVE随机关卡数量,GVE单人关卡随机数量范围,新加入协会，多少分钟后能参与GVE玩法,GVE关卡奖励次数限制,命运卡回收获得的强化卡材料ID,建设挂机彩蛋(等级:次数;),派遣大成功配置（概率;伪随机次数）,捐赠设置(最大存在时间（h）),讨伐挑战物资收集首通消耗多倍开关；1代表不消耗、2代表消耗,讨伐挑战营救任务首通门票开关；1代表不消耗、2代表消耗,衔尾蛇酒吧首通消耗多倍开关；1代表不消耗、2代表消耗,命运卡栏位对应格斗家解锁条件：（槽位：类型：参数）；类型1=格斗家等级，类型2=格斗家境界realm表quality 字段，类型3=格斗家星级；,录像分享CD/秒,3S擂台赛_场景库,3S擂台赛_自身排名上限,3S擂台赛_每日免费挑战次数,3S擂台赛_每日付费挑战,3S擂台赛_换一批使用CD（秒）,3S擂台赛_换一批自动CD（秒）,3S擂台赛_挑战信息自动CD（秒）,3S擂台赛_布阵界面自动CD（秒）,3S擂台赛_战报保存条数,3S擂台赛_积分排行榜显示上限,3S擂台赛_货币ID,3S擂台赛_开启所需格斗家,3S擂台赛_限制格斗家赛季以及数量(上阵，援护),3S擂台赛_首赛季计数参考天数,3S擂台赛_每日排名邮件,3S擂台赛_赛季积分邮件,3S擂台赛_发放声望和积分间隔（秒）,3S擂台赛_允许跳过时间（秒）,3S擂台赛_席位排名结算时间,3S擂台赛_声望补发邮件（赛季结束未领取的声望）,格斗之城随机战斗解锁等级和对应的个数,格斗之城随机战斗存在时间和CD(在10月7号删除),格斗之城挑战事件最大数量,格斗之城随机事件每日刷新的最大次数,战斗回放分享CD(秒),格斗之城随机战斗持续时间(小时)和CD(分钟),竞技场_邮件,公平竞技_机器人等级（最低31级，上下浮动3级）,街区特权体力补偿邮件,1v1切磋_PVP血量加成,黑石博物馆EX值（仅供客户端做普通道具图标显示),新手引导获得大门后重排阵容（taskID+阵容）,举报设置频控（单位秒），1分钟内只能提交1次举报，超过1次，提示【您的操作过于频繁，请稍后再试】,每个玩家每天举报上限10次,举报邮件id,单次受到的技能伤害超过最大生命值万分比X时，获得1点怒气。,格斗竞技_场景库,格斗竞技_自身排名上限,格斗竞技_每日免费挑战次数,格斗竞技_每日付费挑战,格斗竞技_换一批使用CD（秒）,格斗竞技_刷新次数上限,格斗竞技_挑战信息自动CD（秒）,格斗竞技_布阵界面自动CD（秒）,格斗竞技_战报保存条数,格斗竞技_排行榜显示上限,格斗竞技_货币ID,格斗竞技_每日奖励邮件,格斗竞技_允许跳过时间（秒）,格斗竞技_战斗获得积分（胜利：失败：等级）,格斗家羁绊评分最小值和最大值,格斗家羁绊评论长度,进入格斗家羁绊界面 服务器给客户端默认的评论数,格斗家羁绊-每日试炼基础挑战次数,格斗家羁绊-每日试炼Live2D对话显示时间,格斗竞技_排名奖励邮件,援护格斗家属性%附加到上阵格斗家（生命;攻击;防御）,新冠军杯_挑战次数,新冠军杯_匹配时间,新冠军杯_格斗竞技阵容随机赋值冠军杯星级,新冠军杯_货币ID,新冠军杯_段位保护累计上限,新冠军杯_战报累计上限,新冠军杯_前几个赛季为本服赛季,新冠军杯_日奖励邮件,新冠军杯_赛季奖励邮件,普通模式是否可1星扫荡；精英模式是否可1星扫荡（0代表不可，1代表可）,消耗体力特殊掉落,格斗竞技_允许挑战排行榜的名次,格斗家羁绊初始等级,音乐馆普通难度重置消耗（次数：钻石），填0代表无重置,音乐馆精英难度重置消耗（次数：钻石），填0代表无重置,神武试炼换一批的次数,神武试炼复活事件权重变化(最小死亡人数：最大死亡人数：权重值）,神武试炼复活石提升格斗家攻击和血量百分比,神武试炼宝箱事件消耗钻石配置,神武试炼战力低于所选对手过多的战力参数（满足其中一条就给提示）,神武试炼匹配公式参数:A;B;C,协会boss开启时间（星期几）,协会boss开始时间（小时：分钟）,协会boss活动持续时间,协会boss活动预热时间(排行榜清除、鼓励次数可购买),协会boss活动结束后预留结算时间,协会boss活动时间区间对应倍数(前多少秒 倍数),协会boss鼓励次数上限,协会boss鼓励单人购买次数限制,协会boss鼓励单次费用,协会boss鼓励单次效果提升(万分比),协会boss抢夺cd时间,协会boss被抢夺cd时间,协会boss抢夺对方积分比例(万分比),协会boss挑战cd时间,协会boss每日挑战次数,抢夺列表显示第一个排名玩家积分比例(万分比),协会boss掉落宝箱概率(万分比),协会boss宝箱随机开启次数,协会boss开启宝箱cd时间,协会boss宝箱存在时间,协会boss宝箱开启积分分布,协会boss个人积分奖励邮件id,协会boss协会积分奖励邮件id,协会boss玩家积分排行榜最大排名,协会boss协会积分排行榜最大排名,协会boss积分排行榜定时刷新时间（考虑服务器压力，该值不能低于10秒）,协会boss等级计算(排行榜前多少玩家平均等级),协会boss整个活动结算时间(活动结束后多少秒开始结算),协会boss积分段内最大触发宝箱个数,协会boss抢夺战斗场景,酒吧本扫荡开启等级,酒吧本挑战次数上限,排行榜点赞次数,排行榜点赞奖励,抽卡触发保底最大次数,格斗竞技开启扫荡天数,分享将列内容及奖励刷新周期（天）,协会boss鼓励奖励,格斗之城(冠军奖励)对应的事件ID,格斗之城(冠军奖励)对应的奖励,魂珠宝录重置CD,神兵锻造-各个槽位的材料,装备精炼爆率,战法点道具,将魂升星功能解锁等级,八极大厦挂机奖励结算周期（秒）,八极大厦挂机快速收益（次数:花费）,八极大厦挂机快速收益单次时长,八极大厦挂机每日快速收益基础次数,四神幻境每天挑战机会（可叠加）,每日历练次数上限,历练刷新元宝消耗,神武试炼M天未登录N倍找回奖励（废弃）,一番街刷新任务NPC配置（每日刷新数量:刷新间隔时间:每次刷新个数）,主角-初始时装,竞技场每日发奖数量,翻牌奖励前端展示,竞技场胜利随机奖励后端掉落,创建协会所需持有的道具及数量,协会邮件字数上限,协会邮件发送CD（单位：秒）,协会招募发送CD（单位：秒）,协会招募字数上限,协会维持精英人员占比（单位：%）,每日筹募免费次数,每次筹募消耗钻石数,每次筹募提供协会资金数,筹募可获得功勋值,核心打造单日次数上限,打造出传说品质核心保底次数,每次祭祀可获得活跃度,战力值设定上限,强化随机属性的强化等级,已强化经验转换系数,基于基础经验得出作为材料需要消耗的金币比例,秘卷经验道具,协会高级商店解锁需要的协会商店等级,每一期协会高级玩家可购买的总次数,协会高级开放时间,招财进宝 - 钻石消耗,招财进宝 - 暴击权重,招财进宝 - 基本金币数量,招财进宝 - 进度宝箱,协会限制踢人（配置相关功能ID）,冠军杯3 - 开服第几天可以开放,冠军杯3 - 开放时间,组队讨伐 - 天尊触发后多少秒过期,初始格斗家默认上阵配置（格斗家ID:阵位号）,时空裂隙每日挑战次数（废弃）,时空裂隙每日抢夺次数,时空裂隙挑战冷却时间（秒）（废弃）,时空裂隙抢夺冷却时间（秒）（废弃）,时空裂隙挑战Boss积分计算公式参数abc,时空裂隙抢夺积分计算公式参数abc,时空裂隙单次挑战奖励,时空裂隙挑战次数所对应的积分倍率,时空裂隙被抢夺次数限制，每次活动最多抢夺X次,时空裂隙功能开启所需协会等级,狩猎都市功能开启所需协会等级,战斗核心打造（炼宝阁)功能开启所需协会等级,战斗核心槽位开启所需主角等级（槽位号：开启等级；…...),每日累充获取次数所需货币金额,每日累充每次完成充值后可获得的抽奖次数,每日累充每天可获得的抽奖总次数,密卷强化材料,头衔等级增伤,狩猎都市每日挑战次数（重置时若次数小于该值，则重置为该值）,狩猎都市挑战次数每日购买上限（每天最多可购买的次数，每日重置）,狩猎都市挑战次数购买价格（次数下限：次数上限：单次价格；）,一番街系统开启配置(SystemBaseID:StreetTasIDd:SettingID),秘卷上限个数,格斗家觉醒消耗额外道具（觉醒石）,创角默认获得的战术手环ID,资源购买_元气商品ID,每点元气恢复时长,一键生成账号发放的资源,一番街探险藏宝图最大步数,一番街探险累计最大次数,一番街探险玩法每场生成金币最大个数,一番街探险跨天重置补发奖励邮件,一番街新手引导的最后一个ID,开服转盘：前X次无法抽到大奖,开服转盘：庆典兑换券,绑定手机及邮箱的奖励,开服转盘：每X活跃度，自动获得1张庆典兑换券,月签到补签消耗,铜像馆要求刷新次数上限,铜像馆要求刷新钻石消耗,命运卡战役 - 每日免费扫荡次数,命运卡战役 - 每日可购买扫荡次数,命运卡战役 - 购买扫荡次数单次消耗,命运卡战役 - 扫荡掉落,命运卡战役 - 章节宝箱1需要的星数,命运卡战役 - 章节宝箱2需要的星数,命运卡战役 - 章节宝箱3需要的星数,拍卖行货物刷新冷却时间（秒）,拍卖行个人拍卖记录上限,对抗冲刺显示隐藏,主线体力消耗,精英体力消耗,主线经验奖励,精英经验奖励,每日红包领取次数,红包持续时间（天数）,红包存储上限,流失召回 - 需要离线超过多少天才触发,流失召回 - 触发后开放几天,流失召回 - 初始礼包,喜信单日回复次数上限,喜信单日同一好友回复次数上限,喜信保存期限(天),喜信保存数量上限,首充金额要求,首充每日奖励,录像：手动录制的保存上限（场数）,周末福利每日任务随机钻石奖励,协会休闲玩法-坦克初始速度,协会休闲玩法-每个玩家增加的速度,协会休闲玩法-助威增加的速度（百分比）,协会休闲玩法-每个协会的最大助威次数,协会休闲玩法-助威奖励,协会休闲玩法-赛道长度,协会休闲玩法-比赛持续时间（秒）,协会休闲玩法-道具配置（ID|初始获得数量|使用效果:1减速2加速3护盾|持续时间|名称|描述）,协会休闲玩法-BOSS可被攻击次数|BOSS存活时间|减速效果,协会休闲玩法-比赛开始时间,协会休闲玩法-助威消耗,协会休闲玩法-Boss出现的路程区间,协会变色坦克名称,协会玩家在线操作增加奖励,协会玩家在线操作增加奖励最大次数,喜信回复_文字长度,八角铁笼赛开服匹配,八角铁笼赛开服匹配（跨服）,八角铁笼赛公会活跃匹配记录时间（首次）,八角铁笼赛公会活跃匹配记录时间（正式开始）,八角铁笼赛可进入协会等级,八角铁笼赛可进入协会活跃人数,八角铁笼赛可进入经纪人等级（会长）,八角铁笼赛可进入经纪人等级（进入）,协会总战力排行（单服）,协会总战力排行（跨服）,新建协会的固定协会积分,格斗家在生日当天赠送礼物增加的倍数（百分比）,八角协会积分:1:普通擂台 2:巅峰擂台 3:普通黑石能量收集器 4:高级黑石能量收集器5:平局6:对决胜利7对决失败8:轮空,八角会员守备力,八角建筑（擂台/能量器）耐久和防御率:1:普通擂台耐久 普通黑石能量收集器耐久 2:巅峰铁笼擂台耐久 高级黑石能量器耐久,八角每日可攻击次数,八角击败1支队伍扣除守备力1:1支队伍 2:2支队伍3:最低默认扣除守备力,八角击败1个格斗家额外扣除守备力,八角每次攻击建筑（擂台/能量器）物扣除耐久1:普通擂台巅峰擂台 获得积分2 普通能量 巅峰能量 获得积分,八角1:击败1支队伍获得贡献积分2:击败2支队伍获得贡献积分3:最低默认获得的个人积分,八角击败1个格斗家额外获得贡献积分,八角每次攻击建筑（擂台/能量器）物获得贡献积分,无限挑战每个副本挑战次数上线,阵势开启等级,八角会场类型 会场数量 铁笼数量 场景地图 能量棒类型1普通2高级 场景地图,八角铁笼最高能集火的数量,八角每轮奖励(胜利|失败|平),八角赛奖励邮件（1轮次邮件 2 赛季邮件 3 个人积分邮件 ）,八角个人积分奖励邮件,天尊关卡助战奖励次数,天尊关卡助战次数（奖励次数包含于助战次数中）,八角铁笼主会场 分会场安保系数(1分会场2主会场),资源找回-超值特购累计离线最大天数,八角战斗奖励(胜利|失败),每月签到天数,八角战报进攻汇总和防守汇总的显示条数,一键挑战功能同一关卡最多尝试挑战次数,一键挑战功能请求战斗延时,一键挑战功能执行中省略号动效时间间隔,命运卡战役每天最大通关数,冠军杯积分差值,联合对抗，一键邀请冷却时间（秒）,八角分会场攻打黑石能量器需要击破的擂台数量,八角角色战斗的最低守备力伤害,八角第二队新增的buff,芯片重生位置调整,神武试炼战力扩大范围系数,格斗之城精英模式每日累计通关次数（累加）,新加入的协会成员的攻击衰减（百分比）,八角轮空协会每日奖励,联合对抗匹配玩家数据，触发时间：秒,命运卡战役 - 扫荡开启条件（填入具体关卡ID）,136礼包各档位奖励,136礼包一键购买商品ID,136礼包一键购买持续天数,136礼包邮件补发ID,黑石角斗赛开启关闭,反伤伤害上限：不超过受击者最大生命值的%（万分比）,体力领取 - 时间,体力领取 - 体力值,体力领取 - 补领需要的钻石数量,格斗竞技购买次数最大值,好友达到上限是否推送好友申请（0 不推送 1 推送）,黑石角斗赛公告编写最大字数,载具升级消耗itemID,载具上阵位解锁等级,巡街进度宝箱,巡街单日获得能量上限,单次巡街能量消耗,合金弹头-守卫格斗之城-关卡进度计算公式中的常量参数(a;b),载具抽卡，大奖展示条目上限,3s擂台赛 - 开放时间,3s擂台赛 - 每日段位结算邮件,3s擂台赛 - 赛季段位结算邮件,3s擂台赛 - 每日奖励补发id,3s擂台赛 - 赛季奖励补发id,神武试炼一键挑战选牌环节延时显示,3S擂台赛积分差值,命运卡活动签到天数,常世之门活动翻牌需求积分,常世之门活动试炼伤害转积分系数,载具升级等级上限,联合对抗战区开服时间(开服>Xj加入跨区),黑石角斗战区开服时间(不能小于 2202中填写的值),冠军杯赛战区开服时间(开服>Xj加入跨区),3S擂台赛战区开服时间(开服>Xj加入跨区),格斗家排行显示排名数量,格斗家排行榜解锁等级,资源找回系统经验找回系数,探险自动探索功能解锁等级,玩家等级上限,便利店签到：前X次无法抽到大奖,3s擂台赛参数1,3s擂台赛参数2,3s擂台赛参数3,3s擂台赛参数4,时空裂隙抢夺列表显示人数（前N名+后N名）,文芳山书店M天未登录N倍找回奖励（废弃）,拍卖协会活跃积分A=a/时空裂隙协会排名+b/地面疾走的排名+c*本协会前一日累积在线人数+d*协会总募集次数,联合对抗天尊关卡保存上限数量,庆典转盘、每日特饮前X级取特殊卡池,庆典转盘、每日特饮开服前X天取特殊卡池,136礼包一键购买赠送物品,魂珠宝录一键激活方案名称限制,连续n次不出天尊，第N+1次必出,邀请回归-邀请人可成功邀请人数上限,邀请回归-参与活动需要的被邀请人数,邀请回归-每次成功邀请时，邀请人获得的奖励,邀请回归-被邀请人接受邀请时获得的奖励,新功能预热 每日期待礼,新功能预热 版本大礼,新功能预热结束后，持续几小时后关闭（天）,新功能预热 每日期待礼 邮件补发（填入Mailinfo的ID),新功能预热 版本大礼邮件（填入Mailinfo的ID),月基金-开启后立即获得钻石,阵容自定义名称字符长度,邀请回归-区分邀请人、被邀请人身份所需的离线天数,邀请回归-补发邮件ID,打地鼠周几开,心愿系统-每日可发布心愿次数,心愿系统-每个心愿帖所需祝福次数,心愿系统-每个玩家祝福每个心愿贴的限制次数,心愿系统-祝福心愿帖每次所获协会功勋,心愿系统-祝福心愿帖所获协会功勋每日上限,心愿系统-祝福消耗的代币ID以及数量,心愿系统-许愿SSR卡、SR卡、R卡可获得数量,心愿系统-完成每日筹募免费5次可获得代币,心愿系统-完成狩猎都市每日首次挑战可获得代币,心愿系统-完成时空缝隙每日首次挑战可获得代币,心愿系统-协会活跃每日首个宝箱可获得代币,心愿系统-惊喜事件触发概率,心愿系统-祝福记录最多保存数量,心愿系统-每日可祝福次数限制,版更答题Fate联动答题一次题目总数量,终身卡（永居签证）对应的SDKGoodsID,退出协会后的加入限制时间,Fate射箭玩法每日次数,Fate射箭玩法最大 环数（用于校验）,大蛇玩法-签到-高级奖励ID,跨服镜像玩家入池等级限制,违规后默认阵容名称,终极之战观览次数,心愿系统-地面疾走报名可获得代币,大蛇正式活动全服贡献值保底算法常亮参数,冠军杯_日常奖励补领邮件,冠军杯_段位奖励补领邮件,下周有礼 - 奖励,新宿命空间默认阵容,限时特惠 - 免费馈赠礼包,许愿活动 - 每日助力奖励,许愿活动 - 每次助力奖励,都市巡游-分数系数,卢卡尔玩法-签到-高级奖励ID,卢卡尔正式活动全服贡献值保底算法常亮参数,宿命空间攻防技辅,神居古潭手牌最大刷新次数,神居古潭宝箱最大重置天数,神居古潭重置回退步数,神居古潭自动发放buff间隔时间（秒）,神居古潭最大buff层数,新年特卖奖励补发邮件,召回活动开服第X天后才能触发,战魄碎片经验值【品质】【经验值】,战魄升级消耗金币比经验系数（填充10点经验消耗金币100）,召回活动等级条件,战魄格子唤醒条件（格子序号|格斗家品质|格斗家星级）,经纪人性别转换钻石消耗,格斗快报-充值后立即获得的奖励,协会-修改公告天数,协会-每天踢人人数限制,协会-每天加入人数上限,地区货币繁中（地区：货币单位：地区名称key值：地区标识）,136礼包 - 单日打包商品ID,136礼包 - 单日打包立即获得道具,136礼包的7日连购（买6天送1天）的开关,战魄技能更新时间（每次增加新的战魄技能都需要更新这个字段，告知服务器刷新玩家技能）,搏击俱乐部 - 开放时间,搏击俱乐部 - 段位突破结算邮件,搏击俱乐部 - 赛季积分排行结算邮件,搏击俱乐部 - 每日任务奖励补发邮件,搏击俱乐部 - 胜场奖励补发邮件,搏击俱乐部 - 匹配时间,搏击俱乐部 - 每日免费挑战次数,搏击俱乐部 - 每日付费购买次数上限,搏击俱乐部 - 每日付费购购买价格,搏击俱乐部 - 战报保存条数,搏击俱乐部 - 积分排行榜显示上限,万年樱-勾玉等级对应产出道具个数,万年樱-重置按钮CD（秒）,万年樱-勾玉等级对应评分,收藏室拜访记录限制,限时累充_奖励补领邮件
#ID,InitItem,HighHeroNum,LowHeroNum,RankHeros,DefaultHead,DefaultImage,DefaultHeadFrame,PowerRecoverInterval,InitHeroList,DmgRandNum,ArenaInitDivision,ArenaInitELOScore,ArenaBanTime,ArenaChoseTime,ArenaHitPower,ArenaMaxRound,ArenaRoundTime,ArenaClearHitTime,FreeChangeNameTimes,ChangeNameCost,MaxEnglishName,ChatMsgLen,ChatMsgTime,ChatMsgRepeatTime,HeroLvUpCost,BagCapcity,MailEffectiveDay,MaxSoulCount,BlackListCount,RefreshHour,RefreshMin,InitTeam,BattleAtkRatio,BattleDefRatio1,BattleDefRatio2,PVEMaxFury,PVPMaxFury,PVEMaxFuryBean,PVPMaxFuryBean,InitAssistCD,OldMailMax,NewMailMax,AddFuryByKill,AddFuryByKilled,ShieldChange,ShieldReduction,ResourceCopyTicketID,ResourceCopyTicketEveryDay,ResourceCopyTicketMax,ResourceExpCopyMultipleTimes,ResourceGoldCopyTicketCost,ResourceCopyTicketInit,DailyTaskHardNum,AreanScene,AddFuryByattacked,ChatWorldLevelLimit,BanCountDownTime,PickCountDownTime,ArenaRankMax,ForceRecycleSeconds,FastBattle,ChangeNameTaskID,HeroFateCostMax,FateLevelMax,FateStarNumMax,MaxTeamNum,FreeBattleTime,PayBattleTime,ArenaInitialScore,NextRecoverTime,ArenaMaxRefreshCount,BattleStorage,BattleWinPoint,BattleLosePoint,PointRequire1,PointReward1,PointRequire2,PointReward2,PointRequire3,PointReward3,TopPointRule,TopWinPointRule,RCommonCoreChipRate,SRCommonCoreChipRate,SSRCommonCoreChipRate,RTSRCommonCoreChipRate,RTSSRCommonCoreChipRate,FateCardExpKeepRate,RankLVLimit,ArenaImages,DailyShopID,PowerGoodsID,BreakSceneDamageLImit,BreakSceneHPLImit,WaveRemoveBuff,DeadRemoveBuff,ActionRemoveBuff,DefaultSkillLv,HeroLvLimit,HeroMaxLvLimit,BulletMaxLimit,SkipShieldBuff,UnlockSkillByStar,YuanqiID,PowerID,GoldID,JadeID,ChampionCoinID,FightClubCoinID,ArenaCoinID,GloryMoneyID,RoomShowNum,RoomTime,RecruitCD,InvitationTime,EmbattleTime,TeamLoadingTime,TwoMemRule,ThreeMemRule,BufferTime,TeamNameLength,ChampionOpenTime,ChampionSettleDelay,ChampionFirstBot,ChampionLocalSeason,ChampionFirstSeason,ChampionBanTime,ChampionSelectTime,ChampionReadyTime,ChampionBufferTime,ChampionLoadingTime,ChampionRoundTime,ChampionTiredRule,ChampionProtectMax,ChampionReportMax,ChampionRankLocalMax,ChampionRankLocalOut,ChampionRankGlobalMax,ChampionRankGlobalOut,ChampionRankShow,ChampionBotDuan,PrepareTime,ChampionBanRoundEndTime,ChampionPickRoundEndTime,ChampionBanCountDownTime,ChampionPickCountDownTime,ChampionBotBanTime,ChampionBotPickTime,FriendPointID,LocalFriendsNum,CrossFriendsNum,RecentChatNum,RecentTeamNum,GetFPTimes,OfflineMsgNum,FriendRecRule,ApplyNum,FightClubOpenTime,FightClubAwardMailId,FightClubTicket,FightClub_MatchRule,FightClub_FirstBotTimes,FightClub_HowLongBot,FightClub_EmbattleTime01,FightClub_EmbattleTime02,FightClub_LoadingTime,FightClubSceneId,FightClub_BattleTime01,FightClub_RankShow,FightClub_RankRefresh,FightClub_ReportNum,FightClub_PassModeTime,ColdShieldConsumeCountid,MatrixWeekResetCount,MatrixWeekResetExpense,FightClubBotBanTime,FightClubBotPickTime,FightClubBotPickJobLimit,FightClubRoundEndWinNum,FightClubRoundEndLoseNum,RobotMatchTime,AdPopupCD,MailTime,ResourceFateCopyMultipleTimes,FriendSayHello,ExpID,ActiveID,GrowthID,ChampionFightersCondition,SkipGuide,KillAddBuff,ActivityMailID,ChampionAwardDayList,GuildNameLengthLimit,GuildDeclarationLengthLimit,GuildConfirmLevelList,GuildCreateCostItem,GuildChangeNameCostItem,GuildTransferMailId,GuildApplyValidTime,GuildInviteValidTime,GuildApplyCdInfo,GuildMaxApplyNum,GuildPlayerMaxApplyNum,GuildPlayerMaxInviteNum,GuildRecruitCdTime,GuildMaxViceChairmanNum,GuildMaxLogNum,GuildLogValidTime,GuildImpeachTime,GuildActiveAwardMailId,GuildChairmanChangedMailId,GuildRecommendMaxApplyNum,GuildTitleName,GuildShopID,FateCardParseCost,PKHeroLimit,PKInviteTime,PKSceneId,GuildDonateSetting,GuildDonateItemType,GuildDonateAward,GuildDonateLevelLimit,FirstRechargeAward,TaskAwardHeroFormation,StreetBuffSlotUnlockLevel,BigSuccessRewardAddtion,StreetHeroFormulaRealm,StreetRecommendHeroBonus,StreetNormalHeroBonus,StreetIdlingInterval,StreetCharactorMeetReward,GveChapterAwardMailId,GveSectionAwardMailId,GveRandomSectionNum,GveRandomSingleSectionNum,GveJoinGuildTimeLimit,GveAwardTimes,FateCardDecomposeRet,StreetSurpriseNum,BigSuccessRate,GuildDonateTime,ExpInstMultipleSwitch,GoldInstTicketSwitch,BarMultipleSwitch,FateCardSlotLimit,ShareReplayCD,QualifyingScene,QualifyingRankLimit,QualifyingFreeTimes,QualifyingPayTimes,QualifyingRefreshCD,QualifyingAutoRefreshCD,QualifyingInfoCD,QualifyingFormationCD,QualifyingHistorylimit,QualifyingRankShow,QualifyingCoinID,QualifyingHeroNum,QualifyingFormationLimit,QualifyingFirstSeasonDay,QualifyingRankMail,QualifyingPointMail,QualifyingRefreshAwardCD,QualifyingSkipBattleCD,QualifyingDailyAwardTime,QualifyingRemainCoinMail,UnlockRandomEvent,RandomEventCD,FcBossEvnetLimit,FcRandomEventLimit,PlayBackCD,RandomEventTime,ArenaPointMail,FightClubBotLevel,StreetPowerCompensate,PKExtraHP,ExpeditionEXScoreID,TaskFormation,ReportIntervalTime,ReportMaxCount,ReportMailId,AddFuryByHPLost,FightingScene,FightingRankLimit,FightingFreeTimes,FightingPayTimes,FightingRefreshCD,FightingAutoRefreshCD,FightingInfoCD,FightingFormationCD,FightingHistorylimit,FightingRankShow,FightingCoinID,FightingPointMail,FightingSkipBattleCD,FightingGetPoint,HeroFetterScoreLimit,HeroFetterCommentLengthLimit,HeroFetterCommentDefaultCount,HeroFetterChallengeCount,HeroFetterTrialDialogTime,FightingCareerMail,AssistAttrAddPerc,ChampionCupChallengeCount,ChampionCupMatchTime,ChampionCupInitMatchDuanId,ChampionCupCoinID,ChampionCupProtectMax,ChampionCupReportMax,ChampionCupLocalSeason,ChampionCupDayAwardMailId,ChampionCupSeasonAwardMailId,WarSweepFlag,CostEnergyReward,FightingChallengeLimit,HeroFetterInitLevel,WarCommonTypeResetCost,WarSeniorTypeResetCost,JourneyOppoRefresh,JourneyDeadWeight,JourneyReliveStoneInfo,JourneyBoxItemCost,JourneyCombatNtf,JourneyMatchParams,GuildBossOpenDays,GuildBossOpenHourTime,GuildBossActivityTime,GuildBossHotTime,GuildBossExtraBattleTime,GuildBossIntervalMultiple,GuildBossInspireTotalCount,GuildBossInspireMaxPlayerBuyCount,GuildBossInspireCostItems,GuildBossInspireEffect,GuildBossSnatchCdTime,GuildBossBeSnatchedCdTime,GuildBossSnatchScoreRatio,GuildBossChallengeCdTime,GuildBossChallengeTotalCount,GuildBossRankScoreRatio,GuildBossDropBoxRate,GuildBossBoxRandNum,GuildBossOpenBoxCdTime,GuildBossBoxAliveTime,GuildBossRandBoxScoreNum,GuildBossAwardRoleMailId,GuildBossAwardGuildMailId,GuildBossPlayerRankMax,GuildBossGuildRankMax,GuildBossRankRefreshTime,GuildBossLevelParam1,GuildBossBalanceTime,GuildBossMaxDropBoxNum,GuildBossSnatchScene,ResourceFateCopySweepLevel,ResourceFateCopyTimesLimit,RankRoleLikeCount,RankRoleLikeAward,LotteryTimes,FightingSweepOpenTime,SNSShareAward,GuildBossInspireAwardItems,FcFinalsID,FcFinalsReward,JiangHunMenuResetCD,LegendaryWeaponForgeItem,EquipmentRefineRate,TitleScoreID,JiangHunStarUpLevelLimit,ClimbingTowerRewardInterval,ClimbingTowerQuickAwardCost,ClimbingTowerQuickAwardSeconds,ClimbingTowerQuickAwardBaseTimes,TianXiaShiLianChallengeTimes,TianXiaShiLianLiLianTimesLimit,TianXiaShiLianLiLianRefreshCost,JourneyCallBack,IchiBanGaiAdventure,InitFashionId,FightingDailyRewardNum,FightingWinRewardDrop,FightingSweepRewardDrop,GuildCreateNoCostItem,GuildMailMaxWordsCount,GuildMailSendCd,GuildSummonCd,GuildRecruitMsgWordsCnt,GuildEliteMemberPerc,GuildDonateFreeCount,GuildDonateCost,GuildDonateAddMoney,GuildDonateAddExploit,FightCoreMakeDayMax,FightCoreMustGetCount,GuildJiCiActiveAdd,GuildMaxCombatSet,VolumeStrengthRandomLevel,VolumeStrengtExpRatio,VolumeStrengCoinRatio,VolumeStrengItem,AuctionUnlockLimit,AuctionBuyTimesLimit,AuctionOpenTime,GoldenFingerCost,GoldenFingerWeight,GoldenFingerBaseGoldCount,GoldenFingerBox,GuildKickLimitSystem,Champion3UnlockDay,Champion3OpenTime,TeamCrusadeTianZunExpire,InitHeroSlot,GuildBossDayFightCount,GuildBossDayRobCount,GuildBossFightCd,GuildBossRobCd,GuildBossScoreParams,GuildBossRobScore,GuildBossFightAward,GuildBossFightScoreAdd,GuildBossBeSnatchedCount,GuildBossOpenLv,GuildGveOpenLv,Guild3DOpenLv,FightCoreSlotOpenLv,DayDayRechargeYuan,DayDayRechargeTimesAdd,DayDayRechargeMaxTimesAddOneDay,MiJuanStrengthenItems,TitleExtraUpgradeID,GuildGveDayChallenge,GuildGveDayBuyChallengeCount,GuildGveBuyPrice,IChiBanGaiOpen,MaxMijuanCount,HeroWakeCost,InitLegendaryWeapon,YuanqiGoodsID,YuanqiRecoverInterval,GmAccountItems,MaxTreasureProgress,MaxAdventureTimes,MaxCoinLimit,AdventureMailId,AdventureGuideId,ActiveLotteryFinalRewardTimesLimit,ActiveLotteryItem,BindingReward,ActiveLotteryActiveCost,SignInCost,LucarRefreshLimit,LucarRefreshCost,FateCardDungeonSweepFreeTimes,FateCardDungeonSweepBuyTimes,FateCardDungeonSweepBuyCost,FateCardDungeonSweepDropID,FateCardDungeonChapterBox1Star,FateCardDungeonChapterBox2Star,FateCardDungeonChapterBox3Star,BidRefreshCdTime,RoleMaxBidRecord,TeamCrusadeFDRewardDisappearDay,FightCityEventCostNormal,FightCityEventCostSenior,FightCityEventExpAddNormal,FightCityEventExpAddSenior,HBDayGetCount,HBTimeLastDay,HBMaxCount,ComeBackOfflineDayLimit,ComeBackOpenDay,ComeBackInitReward,FriendNewsDayMaxAnswer,FriendNewsDaySameMaxAnswer,FriendNewsSaveDay,FriendNewsSaveCount,FirstRechargeLimit,FirstRechargeDayReward,ReplayBattleRecordMax,WeekendBenefitDailyTaskExtraDiamond,TankStartSpeed,TankSpeedRoleAdd,TankSpeedAssistAdd,TankMaxAssistCount,TankAssistAward,TankFightTotalDistance,TankFightTimeLast,TankFightObject,TankFightBoss,TankFightTimeStart,TankFightAssistCost,TankFightBossAppear,TankImage,TankUseObjAward,TankUseObjAwardMaxCount,FriendEventReply,ChamberMatchStart,ChamberCrossMatchStart,ChamberActiveRecordFirst,ChamberActiveRecordNormal,ChamberGuildLv,ChamberRoleCount,ChamberChairLv,ChamberRoleLv,ChamberGuildCombatRank,ChamberGuildCrossCombatRank,ChamberGuildInitScore,BirthdayExpRate,ChamberGuildScoreAdd,ChamberDefendeValue,ChamberDurability,ChamberAttackCount,ChamberDefenceSub,ChamberBeatHeroSubDefValue,ChamberAttackConstructDef,ChamberBeatScore,ChamberBeatRoleScore,ChamberAttackConstructScore,InfiniteFightDungenMaxCount,BattleArrayLevelLimit,ChamberCount,ChamberMaxFireCount,ChamberRoundReward,ChamberMailId,ChamberRoleMailId,TeamCrusadeTianZunAssistRewardTimes,TeamCrusadeTianZunAssistTotalTimes,ChamberDefenceRatio,FindBackDayMax,ChamberFightAward,SignInMaxDay,ChamberMaxRecordCount,StopBattle,RequestDelay,TimeInterval,FateCardDungeonTimesOneday,Champion3CoeB,TeamRecruitCD,ChamberEnergyAttackNeed,ChamberDefValueSubMin,ChamberTmpAttrb,ChipPositionAmend,JourneyExpandedCoefficient,FightCitySeniorOneDayLimit,ChamberNewJoin,ChamberGuildByeAward,TeamRobotAddSeconds,FateCardDungeonSweepOpen,Gift136GoodsList,Gift136OneKeyBuySDKGoodsID,Gift136OneKeyBuyDays,Gift136MailID,ChamberRaceOpenClose,return_dmg_Max,PowerActivityOpenTime,PowerActivityPowerNum,PowerActivityCostItem,FightingBuyMax,sendfriendntf,MaxChamberNewsCount,VehicleLvUpCost,VehicleLevelLimit,PatrolStreetBox,PatrolStreetDayEnergyLimit,PatrolStreetOnceCost,ProtectFightCityScheduleParams,VehicleCardRecordLimit,Fighting3sOpenTime,Fighting3sDailyMailRewardMailId,Fighting3sSeasonMailRewardMailId,Fighting3sDailyRewardMailId,Fighting3sSeasonRewardMailId,JourneyOneClickBuffDelay,Fighting3sCoeB,FateCardSignDayMax,DouHunActivityPay,DouHunActivityDamageChange,VehicleLvUpMax,RegionTeamOpenLimit,RegionGuildOpenLimit,RegionChampionOpenLimit,Region3SOpenLimit,HeroRankingDisplayNumber,HeroRankingUnlock,FindBackExpRate,AutoExploreUnlock,RoleLevelLimit,ActiveFateCardSignFinalRewardTimesLimit,Fighting3sCoe1,Fighting3sCoe2,Fighting3sCoe3,Fighting3sCoe4,guildboss_snatchcount,BookStoreCallBack,guildactiveparam,TianZunIdMax,ActiveLotteryFirstLevel,ActiveLotteryFirstDay,Gift136OneClickItem,OneKeyPlanNameLengthLimit,MustTriggleTianZunTimes,InvitingBack_MaxInvitingNum,InvitingBack_ActiveInvitingNum,InvitingBack_InvitedReward,InvitingBack_BeInvitedReward,ExpectWarmReward,ExpectFinalReward,ExpectShowDay,ExpectWarmMail,ExpectFinalMail,BuyMonthFundReward,SlotArrayNameLengthLimit,InvitingBack_OfflineDayLimit,InvitingBack_RewardMailId,ShootingGameWeekDay,DayWishCount,WishNeedBless,PlayblessCount,WishBlessGetGuildContri,WishBlessGuildContriMax,WishBlessCost,WishHeroChipGetCount,GuildDonateReward,GveWishReward,GuildBossWishReward,GuildActiveReward,SurpriseEvent,WishBlessRecordCount,DayMaxBlessCount,FateQuestionCount,LifeLongCardGoods,QuitGuildJoinCd,FateArrowGameDailyTimes,FateArrowGamePointMax,OrochiSignPay,RegionRoleLevelLimit,DefaultArrayName,UltimateFightViewCount,JoinTankReward,OrochiScheduleHourAddParam,ChampionDailySupplementMailID,ChampionDuanSupplementMailID,NextWeekGift,DestinySpaceDefault,TimeLimitGoodsDailyReward,HelpWishTimes,HelpWishReward,FateScoreRatio,LucarSignPay,LucarScheduleHourAddParam,DestinySpaceLineUpDefault,AncientPoolBoxMaxRefreshTimes,AncientPoolBoxResetDay,AncientPoolTracebackGrid,AncientPoolGetBuffInterval,AncientPoolMaxBuffCnt,NewYearBenefitsMail,ComeBackServerOpenDay,SpiritExp,SpiritExpGoldRate,ComeBackLevelLimit,SpiritWeakUpCondition,SexChangeCost,LongCardExtraReward,ChangeGuildDeclarLimitDay,KickGuildMemberDayLimit,JoinGuildMemberDayLimit,TwAreaConfig,Gift136OneDayBuySDKGoodsID,Gift136OneDayBuyReward,Gift136MultiDayBuySwitch,SpiritUpdateStamp,FightClubOpenStamp,FightClubDuanRewardMail,FightClubSeasonRewardMail,FightClubDailyRewardMail,FightClubWinRewardMail,FightClubFreeMatchTime,FightClubFreeSignTimes,FightClubBuySignTimes,FightClubBuyPrice,FightClubBattleReportTimes,FightClubRankLimit,SakuraSystemReward,SakuraSystemCDTime,HeroQualityScore,CollectionRoomVisitRecordlimitCnt,TimeLimitRechargeMailId
none,,,,cfg(Hero.csv:ID),cfg(HeadFrame.csv:ID),,cfg(HeadFrame.csv:ID),range(0:1000),cfg(Hero.csv:ID),,,,,,,,,,,,,,,,,,range(0:100),,,range(0:23),range(0:59),cfg(Hero.csv:ID),,,,,,,,,range(0:100),range(0:100),,,,,,,,,,,,,,range(0:100),,,range(0:1000),range(0:10000),,,,range(0:100),,,,,,,,range(0:30),,,,cfg(Item.csv:ID)|range(0:n),,cfg(Item.csv:ID)|range(0:n),,cfg(Item.csv:ID)|range(0:n),,,,,,,,,,cfg(Hero.csv:ID),cfg(ShopMain.csv:ShopID),cfg(ShopGoods.csv:GoodsID),,,,,,,,,,,,cfg(Item.csv:ID),cfg(Item.csv:ID),cfg(Item.csv:ID),,cfg(Item.csv:ID),cfg(Item.csv:ID),cfg(Item.csv:ID),cfg(Item.csv:ID),range(0:20),,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,range(0:100),,range(0:200),,cfg(MailInfo.csv:ID),cfg(Item.csv:ID)|range(0:n),,,,,,,,,,,range(0:50),,,,,,,,,,,,range(0:23)|range(0:59),,,cfg(Item.csv:ID),cfg(Item.csv:ID),cfg(Item.csv:ID),,,,cfg(MailInfo.csv:ID),,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,range(0:n)|range(1:4),range(0:11)|range(0:11),range(0:10),range(0:100),,range(0:11)|range(0:n),cfg(MailInfo.csv:ID),,,,,range(0:n),,,,,,,,,,,,,,,,,,,,,,,,,,,,,cfg(Item.csv:ID),,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,cfg(FightCityEvent.csv:ID),cfg(Item.csv:ID)|range(0:n),,,,cfg(Item.csv:ID),,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,cfg(ShopGoods.csv:GoodsID),range(0:4000),,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
int,vector:SItem[int:typeint:id;int:count],vector:SHeroLimit[int:total;int:lv],vector:SHeroLimit[int:total;int:lv],vector:int[],int,int,int,int,vector:int[],int,int,int,int,int,vector:SHitAddPower[int:hits;int:power],int,int,int,int,int,class:SEnglishNameLength[int:min;int:max],int,int,int,vector:SItemNoCnt[int:type;int:id],int,int,int,int,int,int,vector:int[],float,float,float,int,int,int,int,vector:int[],int,int,int,int,float,float,int,int,int,int,int,int,int,vector:int[],int,int,int,int,int,int,vector:SSkillSet[int:round;int:skillid],int,int,int,int,int,int,vector:SItem[int:id;int:count],int,int,int,int,vector:int[],vector:int[],int,vector:SItem[int:id;int:count],int,vector:SItem[int:id;int:count],int,vector:SItem[int:id;int:count],class:SArenaSP[int:rank;int:win;int:lose],class:SArenaSP[int:rank;int:win;int:lose],int,int,int,int,int,float,int,vector:int[],int,int,int,int,vector:SRemoveBuff[int:counttype;int:countname],vector:SRemoveBuff[int:counttype;int:countname],vector:SRemoveBuff[int:counttype;int:countname],vector:SDefaultSkillLv[int:slot;int:lv],int,int,int,vector:SRemoveBuff[int:counttype;int:countname],vector:SUnlock[int:skillgroup;int:star],int,int,int,class:SItemNoCnt[int:type;int:id],int,int,int,int,int,int,int,int,int,int,vector:TwoMem[int:pos1;int:pos2;int:pos3],vector:ThreeMem[int:pos1;int:pos2],int,int,vector:STimePart[int:begin_hour;int:begin_minute;int:end_hour;int:end_minute],int,int,int,int,int,int,int,int,int,int,string,int,int,int,int,int,int,string,int,int,int,int,int,int,class:SBotBanTime[int:minTime;int:maxTime],class:SBotPickTime[int:minTime;int:maxTime],class:SItem[int:type;int:id;int:count],int,int,int,int,int,int,vector:SFriendRecRule[int:online;int:minlv;int:maxlv;int:num],int,vector:SDayTimePart[int:day;int:begin_hour;int:begin_minute;int:end_hour;int:end_minute],int,vector:SItem[int:id;int:count],vector:rule[int:time;int:winNum],int,class:SFClubRobRandomTime[int:minTime;int:maxTime],string,int,int,vector:int[],int,int,int,int,int,int,int,vector:ResetExpense[int:resetCount;int:expenseCount],class:SBotBanTime[int:minTime;int:maxTime],class:SBotPickTime[int:minTime;int:maxTime],int,int,int,class:MatchTimeRang[int:minTime;int:maxTime],int,class:SMailTime[int:hour;int:min],int,string,int,int,int,int,vector:int[],vector:int[],int,vector:int[],class:SGuildNameLength[int:min;int:max],class:SGuildDeclarationLength[int:min;int:max],vector:int[],vector:SItem[int:type;int:id;int:count],vector:SItem[int:type;int:id;int:count],int,int,int,class:SGuildAppyCdInfo[int:time;int:level],int,int,int,int,int,int,int,int,int,int,int,vector:SGuildTitleName[int:title_id;string:title_name],int,vector:SFateCardParseCost[int:itemid;int:costtype;int:goldcost;int:propvalue;string:name],int,int,vector:int[],vector:int[],vector:int[],vector:SItem[int:id;int:count],int,vector:SItem[int:id;int:count],class:Param2[int:p1;int:p2],vector:int[],int,int,int,int,vector:int[],vector:SItem[int:id;int:count],int,int,int,class:SRandomSingleSectionRange[int:minNum;int:maxNum],int,int,vector:int[],vector:StreetSurpriseUnit[int:level;int:num],class:SBigSuccessRate[int:rate;int:count],int,int,int,int,vector:SFateCardSlotLimit[int:slot;int:type;int:value],int,vector:int[],int,int,vector:SItem[int:id;int:count],int,int,int,int,int,int,int,int,vector:SQualifyingHeroLimit[int:formation;int:assist],int,int,int,int,int,int,int,vector:SLV2Value[int:lv;int:value],vector:SEventCD[int:keepTime;int:cd],int,int,int,vector:SEventTime[int:keepHour;int:cdMin],int,class:SFightClubBotLevel[int:minLevel;int:offset],int,int,int,vector:int[],int,int,int,int,vector:int[],int,int,vector:SItem[int:id;int:count],int,int,int,int,int,int,int,int,int,vector:SFightingGetPoint[int:win;int:lose;int:lv],class:SHeroFetterScore[int:minScore;int:maxScore],class:SHeroFetterCommentLength[int:min;int:max],int,int,int,int,vector:int[],int,class:SChampionCupMatchTime[int:minTime;int:maxTime],vector:int[],int,int,int,int,int,int,vector:int[],vector:SCostEnergyReward[int:energy;int:dropId],int,vector:SHeroFetterInitLevel[int:heroId;int:fetterLevel],vector:SWarCost[int:times;int:costNum],vector:SWarCost[int:times;int:costNum],int,vector:SDeadWeight[int:min;int:max;int:weight],class:SItem[int:id;int:count],vector:SItem[int:id;int:count],vector:int[],vector:int[],vector:int[],class:SGuildBossOpenHourTime[int:hour;int:minute],int,int,int,vector:SGuildBossIntervalMultiple[int:time;int:multiple],int,int,vector:SItem[int:id;int:count],int,int,int,int,int,int,int,int,class:SGuildBossBoxRandNum[int:min;int:max],int,int,class:SGuildBossRandBoxScoreNum[int:min;int:max],int,int,int,int,int,int,int,int,vector:int[],int,int,int,vector:SItem[int:id;int:count],int,int,class:SItemAndDay[int:id;int:count;int:day],vector:SItem[int:id;int:count],int,vector:SItem[int:id;int:count],int,vector:SLegendaryWeaponForgeItem[int:type;int:item_id;int:skill_upgrade_id],vector:SItem[int:id;int:count],int,int,int,vector:SItem[int:type;int:id;int:count],int,int,int,int,vector:SItem[int:type;int:id;int:count],vector:SJourneyCallBack[int:day;int:combo],class:SItem[int:type;int:id;int:count],int,int,int,int,class:SItem[int:type;int:id;int:count],int,int,int,int,int,int,vector:SDonateCost[int:min;int:max;int:type;int:id;int:count],class:SItem[int:type;int:id;int:count],class:SItem[int:type;int:id;int:count],int,int,int,int,vector:int[],int,int,vector:int[],int,int,vector:SAuctionOpen[int:day_of_week;int:begin;int:end],vector:SGoldenFingerCost[int:min;int:max;int:count],vector:SGoldenFingerWeight[int:weight;int:bei],int,vector:SGoldenFingerBox[int:times;int:type;int:id;int:count],vector:int[],int,class:SChampion3OpenTime[int:day_of_week;int:hour;int:minute;int:total_second],int,vector:SInitSlot[int:heroid;int:slotid],int,int,int,int,class:SFightBossScore[int:score;int:a;int:b;int:c],vector:SRobScore[int:min;int:max;int:a;int:b;int:c],class:SItem[int:type;int:id;int:count],vector:SFighScoreAdd[int:count;int:add],int,int,int,int,vector:SFightCoreSlotOpen[int:slotid;int:lv],int,int,int,vector:SMiJuanStrengthen[int:table_type;int:item_id;int:exp],int,int,int,vector:SGuildGveBuyPrice[int:min;int:max;int:type;int:id;int:count],vector:SIChiBanGaiOpen[int:sbid;int:taskid;int:sid],int,class:SItemNoCnt[int:type;int:id],class:SItem[int:type;int:id;int:count],int,int,vector:SItem[int:type;int:id;int:count],int,int,int,int,int,int,class:SItemNoCnt[int:type;int:id],vector:SItem[int:typeint:id;int:count],int,class:SItem[int:type;int:id;int:count],int,vector:SItem[int:typeint:id;int:count],int,int,class:SItem[int:type;int:id;int:count],int,int,int,int,int,int,int,class:SItem[int:type;int:id;int:count],class:SItem[int:type;int:id;int:count],int,int,int,int,int,int,int,vector:SItem[int:typeint:id;int:count],int,int,int,int,int,vector:SFirstRechargeDayReward[int:day;int:type;int:itemid;int:itemcount],int,vector:SDiamondWeight[int:diamond;int:weight],int,int,int,int,class:SItem[int:type;int:id;int:count],int,int,vector:STankObject[int:id;int:count;int:type;int:value;int:time;string:name;string:desc],class:STankBoss[int:attackcount;int:lifetime;int:subspeed],int,class:SItem[int:type;int:id;int:count],vector:SBossAppear[int:start;int:end;int:first;int:second;int:third;int:fourth],vector:string[],class:SItem[int:type;int:id;int:count],int,int,int,int,int,int,int,int,int,int,int,int,int,int,vector:SChamberGuildScoreAdd[int:type;int:score],int,vector:SChamberDurability[int:type;int:consdur;int:energydur;int:alldefence;int:everydefencesub],int,vector:SChamberDefenceSub[int:type;int:defencesub],int,vector:ChamberAttackSubDura[int:type;int:construct;int:energy;int:scoreget],vector:SBeatScore[int:type;int:score],int,int,int,int,vector:SChamberCount[int:type;int:count;int:construct;int:energytype;int:sceneid],int,string,vector:SChamberMailId[int:type;int:mailid],int,int,int,vector:SChamberDefenceRatio[int:type;int:defencevalue;int:defencevaluesub],int,string,int,int,int,int,int,int,int,int,vector:SChamberEnergyAttack[int:id;int:count],int,vector:int[],string,vector:int[],int,int,vector:SItem[int:type;int:id;int:count],int,int,vector:SActivityGift136[int:idx;int:sdkgoods;int:type;int:itemid;int:itemcount],int,int,int,int,int,vector:STimePart[int:begin_hour;int:begin_minute;int:end_hour;int:end_minute],vector:SItem[int:type;int:id;int:count],class:SItem[int:type;int:id;int:count],int,int,int,vector:SItem[int:type;int:id;int:count],vector:int[],vector:SPatrolStreetBox[int:score;int:type;int:id;int:count],int,int,vector:int[],int,class:SChampion3OpenTime[int:day_of_week;int:hour;int:minute;int:total_second],int,int,int,int,int,int,int,vector:int[],int,int,int,int,int,int,int,int,float,int,int,int,int,float,int,int,int,vector:SBookStoreCallBack[int:day;int:combo],class:SGuildActiveParam[int:param_a;int:param_b;int:param_c;int:param_d],int,int,int,vector:SItem[int:type;int:id;int:count],class:SOneKeyPlanNameLength[int:min;int:max],int,int,int,vector:SItem[int:type;int:id;int:count],vector:SItem[int:type;int:id;int:count],vector:SItem[int:type;int:id;int:count],vector:SItem[int:type;int:id;int:count],int,int,int,class:SItem[int:type;int:id;int:count],class:SSlotArrayNameLength[int:min;int:max],int,int,vector:int[],int,int,int,class:SItem[int:type;int:id;int:count],int,class:SItem[int:type;int:id;int:count],map:int[],class:SItem[int:type;int:id;int:count],class:SItem[int:type;int:id;int:count],class:SItem[int:type;int:id;int:count],class:SItem[int:type;int:id;int:count],vector:SSurpriseEvent[int:hour;int:rate],int,int,int,int,map:int[],int,int,int,int,string,int,class:SItem[int:type;int:id;int:count],int,int,int,vector:SItem[int:type;int:id;int:count],vector:int[],vector:SItem[int:type;int:id;int:count],int,vector:SItem[int:type;int:id;int:count],int,int,int,map:int[],int,int,int,int,int,int,int,map:int[],int,int,string,vector:SItem[int:type;int:id;int:count],vector:SItem[int:type;int:id;int:count],int,int,int,string,int,vector:SItem[int:type;int:id;int:count],int,string,class:SChampion3OpenTime[int:day_of_week;int:hour;int:minute;int:total_second],int,int,int,int,int,int,int,vector:SItem[int:type;int:id;int:count],int,int,vector:SItem[int:type;int:id;int:count],int,vector:int[],int,int
0,1:1010001:120;1:1010002:30;1:30002011:2;1:30002012:2;1:30002013:2;1:30002014:2;,,,1002;1003;1004;1007;1008;1009;1011;1012;1013;1014;1019;1021;1031;1032;1033;1034;1036;1038;1044;1060;1062;1201;1202;1203;,1021062,1,1030001,360,1002;1062;,100,1,1000,20,60,30:20;45:30;60:40,99,30,10,1,100,4:12;,150,8,15,1:3010301;1:3010302;1:3010303;1:3010304;1:3010305;1:3010306;,1000,15,2000,50,5,0,1062;0;1002;0,1,150,500,1000,1000,2,2,0;0;,80,80,0,0,1,0,1034101,0,3,1,1,1,4,1012;1018;1026;1030;1034;,0,26,2,2,1000,3600,3:99999;3:99998;3:99997;4:99999;4:99998;4:99997;5:99999;5:99998;5:99997;6:99999;6:99998;6:99997;7:99999;7:99998;7:99997;8:99999;8:99998;8:99997;9:99999;9:99998;9:99997;10:99999;10:99998;10:99997;,91022,7,50,6,5,5,1020002:20;1020002:30;1020002:40;1020002:50;1020002:60;,1200,2,99,10,5;4;3,0;0;0,9,1020301:20;1020001:2000,12,1020002:20;1020001:3000,15,1020301:30;1020001:5000,10:30:0;,,1,10,100,10,20,1,10,1007;1201;1060;1021;1203;1202;1031;1033;1044;1063;1012;1013;1032;1002;1009;1019;1062;1004;1014;1034;1003;,100,107001,20,60,2:1;2:2;2:4;2:6;2:7;0:31031111;0:31031310;0:31010112;1:8015;0:901513;0:901518;0:31062531;0:31036510;0:31036511;0:31012310;1:9006;,2:1;2:2;2:4;2:5;2:7;3:6001;0:31062531;0:31036510;0:31036511;0:31012310;,0:0;,1:1;2:0;3:1;4:1;5:1;6:0;,1,60,200,1:12;,1:1;2:2;3:1;4:1;,1010002,1010001,1020001,1:1020002,1030101,1020401,1020301,1020301,10,120,15,60,30,60,1:3:5;2:4:6,1:3;2:5;4:6,5,5,12:00:14:00;18:00:22:00;,600,5,1,1,23,16,45,3,60,20,,5,10,200,2000,200,2000,610:200,5,3,2,2,2,2,1:2;,1:2;,1:1020501:1;,100,50,10,20,60,100,0:5:10:1;0:0:5:6;0:-5:0:1;6:-5:5:0;9:-5:5:0;12:-10:10:0;,50,1:00:00:24:00;2:00:00:24:00;3:00:00:24:00;4:00:00:24:00;5:00:00:24:00;6:00:00:24:00;7:00:00:24:00,348,0:0;1020001:5000;1020001:5000;1020001:5000;1020001:10000;1020001:10000;1020001:10000;1020001:15000;1020001:15000;1020001:15000;1020002:20;1020002:20;1020002:20;1020002:50;1020002:50;1020002:50;,5:0;10:1;15:2;20:3;25:4;30:9,2,90:120;,45,2,60,1033;1029;1027;1030;1034;,20,10,60,10,60,901103,1,1:10;2:20;5:40;10:60;20:100,1:2;,1:2;,3,9,3,2:5;,360,5:00;,0,Friend_Tips_15,1030001,1032001,1033001,3,91063;91081;91091;910530;91093;990121;91077;914011;99028;103001;91090;91100;91094;91110;99034;91109;91130;91181;91193;91150;91121;99030;990161;91076;990182,40000000;40000001;,350,4;7;,4:12;,0:100;,0;30;40;50;,1:1020002:300,1:1020002:200,401,1440,1440,60:0;,100,20,20,120,3,50,5256000,4320,402,403,30,1:GuildTitle1;2:GuildTitle2;3:GuildTitle3,102,4031001:1020001:1000:500:FateCard_Desc_0120;4031002:1020001:10000:500:FateCard_Desc_0121;4031003:1020001:50000:500:FateCard_Desc_0122;4031004:1020001:50000:500:FateCard_Desc_0123;4031005:1020001:50000:500:FateCard_Desc_0124;4031006:1020001:50000:500:FateCard_Desc_0125;,3,20,1012;1018;1026;1030;1034;,2;1;8,4011108;4011109;4011110;4011111;4011112;,1040003:20,2,2011204:1;1020001:10000;3010301:2;4010101:2,91033:1;,0.0438078703704,0,2,20,10,4;8;12;,1050001:5000,404,405,5,2:3;,480,10,3040;3039;3038;,60:3,0:9999,12,1,1,2,1:1:1;2:1:1;3:1:1;4:1:1;,5,1012;1018;1026;1030;1034;,5000,5,1020002:50;1020002:50;1020002:100;1020002:100;1020002:200;,2,30,45,90,10,200,1030102,3,3:1;4:1;4:2,7,,406,1800,3,22,407,21:1;26:2;30:3;,6:6;6:6;6:6;,6,6,30,6:10;6:10;6:10;,347,31:3;,409,0,1030106,910321;0;0;1062;1002;0;0;,60,10,601,10000,1004;1047;1026;1030;1034;,5000,10,1020002:10;,5,5,90,90,10,200,1020301,508,3,2:1:35;2:2:99,1:5;,1:40;,10,10,5,509,1500;2000;1500;,6,1:3;,110;111;112;113;,1030101,5,10,2,507,506,1;1,6:1030100102;15:1030100103;35:1030100104;,11,1062:1;,0,1:100;2:200;3:300,3,1:2:1000;3:5:2000;6:10:4000;11:9999:8000,1020601:1001,1:0;2:20;3:30;4:40;5:60;6:80;7:120;8:180;9:240;10:300;,30;50000,50;50;5,1;2;3;4;5;6;7;,5:00,54000,120,1,240:1;420:2;600:3;,60,1,1020002:50;,100,10,180,1000,10,3,11000,10,10:20;,60,120,1:100000,410,411,50,50,10,100,300,3,1081;1082;1083,5,3,3,1020001:5000;,30,4,1:1020002:30;,1040003:1000;,10100105,1020002:200;1020102:10;1020001:100000;1020006:3000;,2,1:4020114:50101;1:4020115:50201;1:4020116:50301;,8:1;20:1;,8100001,40,300,1:1020002:0;1:1020002:100;1:1020002:200;,7200,3,15,3,1:1020002:0;1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50;1:1020002:50;,1:1;2:2;3:3;4:4;5:5;,1:0:3,44001,100,3890522,3890529,1:1040005:1,100,14400,120,100,25,5,6:20:1:1020002:10;21:50:1:1020002:20;51:100:1:1020002:50;,1:1040001:2000,1:1040003:500,100,20,1,900000000,3;6;9;12;15;18;21;24,1,10,1070101;1070201;1070301;,1,100,1:25200:57600;2:25200:57600;3:25200:57600;4:25200:57600;5:25200:57600;6:25200:57600;7:25200:57600;,1:2:0;3:20:10;21:50:20;51:500:30;,70:1;20:2;10:5;,200,20:1:1000047:1;50:1:1000048:1;100:1:1000048:1;,50105;50108;70800;,4,1:5:0:579600,0,1002:0;1062:1;,3,3,180,180,50:1000:30:5000000;,1:10:30:30:10;11:30:30:20:8;31:100:30:15:6;101:999999999:30:10:4;,1:1040003:1000,1:1;2:2;3:3;,5,1,1,1,1:26;2:30;3:34;4:38;,120,1,1,1:4020114:10;1:4020115:20;1:4020116:30;,33000001,5,10,1:5:1:1020002:10;6:10:1:1020002:20;,50501:5:1660;50502:6:1661;50504:7:1662;30400:8:1681,300,1:30002301,4:4001:1;,107002,3600,1:1020002:999999;1:1020001:9999999;1:1040003:999999;1:3010101:999;1:3010201:999;1:3010301:999;1:3010302:9999;1:3010303:9999;1:3010304:9999;1:30000101:9999;1:30000102:9999;1:30000103:9999;1:450001:999999;1:450002:999;1:450003:999999;1:450005:9999;1:450004:999;1:1000069:999;1:1040007:9999;1:1040008:9999;1:1020102:999;1:1020101:999;1:1040005:999;1:30002301:999;1:8400004:99;1:8400005:99;1:8400006:99;1:8400007:99;1:8400008:99;1:1000001:99;1:1000003:999;1:1000015:99;1:1000017:999;1:1070501:999;1:8000026:999;1:80100201:999;1:80100301:999;1:490001:999;1:490002:999;1:90000001:50000;1:1000215:999;1:1000216:999;1:1000217:999;1:1000218:999;1:1000219:999;1:1000220:999;1:1000221:999;1:1000222:999;1:1000223:999;1:1000224:999;1:1000225:999;1:1000226:999;1:1000227:999;1:1000228:999;1:1000229:999;1:1000230:999;1:1000231:999;1:1000232:999;1:1000233:999;1:1000234:999;1:1000336:999;1:1000337:999;1:1000338:999;1:1000339:999;1:1000340:999;1:1000028:999;1:1000029:999;1:1000030:999;1:1000031:999;1:1000032:999;1:1070101:999;1:1070201:999;1:1070301:999;1:480001:999;1:480002:999;1:480003:999;1:480004:999;1:1000345:999;1:1000013:999;1:1000318:999;1:1000319:999;1:1000320:999;1:1000321:999;1:1000322:999;1:1000323:999;1:1000324:999;1:1000270:999;1:1000271:999;1:1000272:999;1:1000273:999;1:1000415:999;1:1000416:999;1:1000417:999;1:1000418:999;,12,99,3,1000,3130,8,1:7050001,1:1020002:200,150,1:1020002:20,5,1:1020002:0;1:1020002:100;1:1020002:150;1:1020002:200;1:1020002:300;1:1020002:300;1:1020002:300;1:1020002:300;1:1020002:300;1:1020002:300;,10,10,1:1020002:20,7072780,5,10,15,10,20,30,1:1010001:5,1:1010001:5,5,5,50,15,50,7,7,1:1020002:888;1:10000102:800;1:1020001:666666;,5,3,30,300,6,1:1:1000064:1;2:1:1020002:888;3:1:1020102:10;,60,5:30;10:40;15:30;,100,5,5,10,1:1040003:2000,70000,300,1:5:1:1:10:TankFightItemName_1:TankFightItemDes_1;2:5:2:1:10:TankFightItemName_2:TankFightItemDes_2;3:0:1:20:15:TankFightItemName_3:TankFightItemDes_3;4:0:3:0:15:TankFightItemName_4:TankFightItemDes_4,120:15:50,54000,1:1020002:50,5000:7000:0:180:0:0;20000:22000:0:240:1:0;40000:42000:0:300:1:1,Char_Battle_5002_TKC_01:TankFaight_Color01;Char_Battle_5004_TKC_01:TankFaight_Color02;Char_Battle_5006_TKC_01:TankFaight_Color03;Char_Battle_5005_TKC_01:TankFaight_Color04,1:1040003:500,5,50,8,24,7,3,2,10,40,40,100,500,8000,200,1:5;2:10;3:50;4:300;5:700;6:1000;7:0;8:500,300,1:1000:5000:20:5;2:2000:20000:50:25,10,1:10;2:20;3:5,1,1:200:300:30;2:300:400:30,1:35;2:50;3:20,2,30,999,200,1:7:8:1:1004;2:1:4:2:1004,8,1:1020011:1500;1:1020002:1000|1:1020011:1000;1:1020002:700|1:1020011:1250;1:1020002:800,1:1057;2:1058;3:1059,351,1,20,1:20:5;2:50:25,5,1:1020011:100;1:1040003:1200|1:1020011:100;1:1040003:1200,28,100,1,0.4,0.4,30,400,10,1:6;2:6;3:6;4:6;5:6;6:6;7:6;8:4,5,1031;1032;1033,400004:80:-524:0;,1500;1500;1500,30,100,1:1020011:100;1:1040003:1200,20,1015,1:0:1:500001:1;2:10600001:1:500002:1;3:10600002:1:500003:1;4:10600003:1:500004:1;,10600000,7,712,1,8000,12:00:16:00;18:00:23:00;,1:3010101:5;1:3010101:5;,1:1020002:50,2000,0,400,1:1000270:1;1:1000271:1;1:1000272:1;1:1000273:1;,63;63;63;,120:1:1020002:100;240:1:1020002:100;360:1:1020002:100;,300,30,30;350;5,100,7:5:0:579600,1109,1110,1111,1111,0.8,400,10,10;20;30;50;80;100;100;150;200;200;200;300;,6666,150,8,8,8,1,100,26,0.5,60,120,4,2500,0.7,30,300,20,1:1;2:2;3:3;4:4;5:5;,500:500:6:1,99,34,3,1:1020002:2180;,4:6;,7,15,9,1:1000070:1;1:1020002:10;1:1020001:8888;,1:1000070:5;1:1020002:66;1:1020001:88888;,1:1000041:1;1:1020001:88888;1:1020002:88,10:500001:1;1:1020102:10;1:1020002:2888;1:1000271:20,2,1132,1131,1:1020002:980,4:6;,2,1134,6:7;,2,2,1,1:1040003:1000;,999999999,1:1000316:1;,4|2;3|4;2|4;,1:1000316:1;,1:1000316:1;,1:1000316:1;,1:1000316:1;,2:10;4:15;8:20;12:25;18:35;24:45;30:55;36:70;42:85;48:100;,15,9999999,5,12000001,1|0;2|1;3|2;4|4;5|8;,2,40,11900001,30,Teamtips_JP_01,10,1:1000316:1;,37500,1055,1112,1:1020102:5,1001;1002;1003;1004;1005;1006;1007;1008;1009;1010;1011;1012;1013;1014;1015;1016;1017;1018;1019;1020;1021;1022;1023;1024;1025;1026;1027;1028;1029;1030;1031;1032;1033;1034;1035;1036;1037;1038;1039;1040;1041;1042;1043;1044;1045,1:1020001:18888;1:450003:200;,10,1:450003:200;,50000,12700001,37500,1|461062006;2|461042006;3|461303006;4|461020006;,19,28,250,21600,50,1184,14,2|10;3|25;4|60;,10,13,1:5:1;2:5:3;3:5:5,1:1020002:0;1:1020002:1000;,1:1020102:10;1:1020002:120,3,10,200,TWD:NT$:locationtip1:TW;HKD:HK$:locationtip2:HKMO;SGD:S$:locationtip3:SG;MYR:RM:locationtip4:MY;USD:US$:locationtip5:ZZ;,10600004,1:1020002:300,1,2023-2-10 05:00:00,3:5:0:432000,1211,1212,1213,1214,1:2;,3,10,1:1020002:50;1:1020002:50;1:1020002:100;1:1020002:100;1:1020002:200;1:1020002:500;1:1020002:500;1:1020002:500;1:1020002:500;1:1020002:500,10,200,1:1000600:1;1:1000600:2;1:1000600:3;1:1000600:4;1:1000600:5;,60,10;20;30;40;50;,20,1230
