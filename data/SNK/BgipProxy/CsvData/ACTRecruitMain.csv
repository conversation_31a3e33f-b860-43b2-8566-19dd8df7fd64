SystemOpen表的ID,系统ID,连抽次数,心愿单开启条件,需求道具ID,每次招募额外的货币掉落,每招募X次可获得额外奖励 次数,每招募X次可获得额外奖励 奖励,可以被选为心愿单的道具,心愿默认 有的卡池必须设置心愿 但不支持玩家手动选择,心愿数量,心愿单概率,心愿自动 在未设置心愿时 若达到心愿保底次数 则从可选心愿中随机一个,心愿保底次数,心愿单每日是否重复抽取,掉落ID,每日招募次数上限,是否有累计奖励,奖励补发邮件ID,积分奖励计算道具
ID,SystemID,NumOfDraw,WiseOpenRlue,CostItemID,ExtraDrop,CumulateTimes,CumulateReward,WishHeroIds,WishDefault,WishNum,WishWeight,WishAuto,WishGuarantee,WishCanRepeat,DropID,TimeLimit,HaveTotalRwd,MailID,LocalServerScoreItem
,,,,,,,,,,,,,,,,,,,
int,int,int,int,vector:SItem[int:type;int:id;int:count],vector:SItem[int:type;int:id;int:count],int,vector:SItem[int:type;int:id;int:count],vector:SItem[int:type;int:id;int:count],int,int,int,int,int,int,int,int,int,int,vector:SItem[int:type;int:id;int:count]
6700401,67004,5,0,1:61101001:1;,1:1020001:500,0,,2:4020209:60;1:1000460:1,0,1,10000,1,50,1,7072748,1000,0,0,
6450401,64504,5,0,1:80100101:1;,1:1000508:10,0,,3:1025:1;3:1026:1;3:1027:1;,0,1,10000,1,35,1,6450401,1000,0,0,
6610101,66101,5,0,1:61101003:1;,1:1020001:500,0,,2:6100401:100;2:6130101:100;2:6106201:100;2:6101301:100;1:1090102:200;,0,1,10000,1,40,1,7072937,5000,0,0,
6610102,66101,5,0,1:61101003:1;,1:1020001:500,0,,2:6102401:100;2:6104201:100;2:6100401:100;2:6130101:100;2:6106201:100;2:6101301:100;1:1090102:200;,0,1,10000,1,40,1,7072937,5000,0,0,
6610103,66101,5,0,1:61101003:1;,1:1020001:500,0,,2:6104801:100;2:6102401:100;2:6104201:100;2:6100401:100;2:6130101:100;2:6106201:100;2:6101301:100;1:1090102:200;,0,1,10000,1,40,1,7072937,5000,0,0,
6610104,66101,5,0,1:61101003:1;,1:1020001:500,0,,2:6104801:100;2:6102401:100;2:6104201:100;2:6100401:100;2:6130101:100;2:6106201:100;2:6101301:100;1:1090102:200;,0,1,10000,1,40,1,7072937,5000,0,0,
6620801,66208,5,0,1:80100601:1;,1:1000591:10,0,,3:1721:1;3:1723:1;3:1725:1;,0,1,10000,1,40,1,6620801,1000,1,1192,1:1041001:1
6471101,64711,5,0,1:80100101:1;,1:1000622:10,0,,3:1206:1;3:1207:1;,0,1,10000,1,40,1,6471101,1000,1,1232,
6640301,66403,5,0,1:80100101:1;,1:1020001:500,0,,3:1031:1;3:1033:1,0,1,10000,1,40,1,6640301,1000,0,0,
6640401,66404,5,0,1:80100101:1;,1:1020001:500,0,,3:1403:1;3:1404:1,0,1,10000,1,40,1,6640401,1000,0,0,
6640501,66405,5,0,1:80100101:1;,1:1020001:500,0,,3:1026:1;3:1027:1;3:1025:1,0,1,10000,1,40,1,6640501,1000,0,0,
6640302,66403,5,0,1:80100101:1;,1:1020001:500,0,,3:1031:1;3:1033:1,0,1,10000,1,40,1,6640301,1000,0,0,
6640402,66404,5,0,1:80100101:1;,1:1020001:500,0,,3:1403:1;3:1404:1,0,1,10000,1,40,1,6640401,1000,0,0,
6640502,66405,5,0,1:80100101:1;,1:1020001:500,0,,3:1026:1;3:1027:1;3:1025:1,0,1,10000,1,40,1,6640501,1000,0,0,
6650301,66503,5,0,1:80100102:1;,1:10000103:10,40,2:5010003:10,,0,1,10000,1,0,1,6650301,1000,0,0,
6650401,66504,5,0,1:80100102:1;,1:10000103:10,40,2:5010006:10,,0,1,10000,1,0,1,6650401,1000,0,0,
6650501,66505,5,0,1:80100102:1;,1:10000103:10,40,2:5010007:10,,0,1,10000,1,0,1,6650501,1000,0,0,
6660101,66601,5,0,1:61101001:1;,1:1020001:500,0,,2:4020209:60;2:4020210:60;,0,1,10000,1,40,1,6660101,5000,0,0,
6700402,67004,5,0,1:61101001:1;,1:1020001:500,0,,2:4020211:60;1:1000460:1,0,1,10000,1,50,1,7072748,1000,0,0,
6800501,68005,5,0,1:80100101:1;,1:1000682:10,0,,3:1091:1;3:1402:1;,0,1,10000,1,35,1,6800501,1000,0,0,
6901201,69012,5,0,1:80100101:1;,1:1000692:10,0,,3:1088:1;3:1045:1;,0,1,10000,1,40,1,6901201,1000,1,1258,
