ID,基础速率（秒：个数）,安全容量,单位超员人数,单位超员减益,减益上限,攻击消耗机会,激斗期间消耗机会,占领据点积分,协会加成,据点加成,协会加成触发需要人数
ID,ProduceSpeed,StationNum,UnitOverNum,UnitOverRate,MaxDecrease,AttackSpend,TotalWarSpend,CityFractionScore,GuildBonus,CityBonus,GuildBonusNeed
,,,,,,,,,,,
int,vector:int[],int,int,int,int,int,int,int,int,int,int
1000,60;1,0,0,0,0,0,0,0,0,0,0
2000,60;2,100,10,1000,5000,2,0,200,1000,2000,20
3000,60;3,60,10,1000,5000,2,0,500,1000,2000,20
4000,60;5,60,10,1000,5000,2,0,1000,1000,2000,20
