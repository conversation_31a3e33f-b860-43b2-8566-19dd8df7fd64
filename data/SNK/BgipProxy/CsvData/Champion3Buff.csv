序号,类型 1-连胜 2-连负,场次-低,场地-高,属性
ID,Type,Min,Max,SkillUpgradeID
range(0:n),range(1:2),,,
int,int,int,int,vector:int[]
1,1,3,3,2031;2032;2033;2034;
2,1,4,4,2041;2042;2043;2044;
3,1,5,5,2051;2052;2053;2054;
4,1,6,6,2061;2062;2063;2064;
5,1,7,7,2071;2072;2073;2074;
6,1,8,8,2081;2082;2083;2084;
7,1,9,9,2091;2092;2093;2094;
8,1,10,99,2101;2102;2103;2104;
9,2,3,3,1031;1032;1033;1034;
10,2,4,4,1041;1042;1044;1044;
11,2,5,5,1051;1052;1053;1054;
12,2,6,6,1061;1062;1063;1064;
13,2,7,7,1071;1072;1073;1074;
14,2,8,8,1081;1082;1083;1084;
15,2,9,9,1091;1092;1093;1094;
16,2,10,99,1101;1102;1103;1104;
