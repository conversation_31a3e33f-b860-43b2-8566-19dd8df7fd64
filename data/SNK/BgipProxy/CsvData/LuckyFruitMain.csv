SystemID,消耗,产出道具,排行道具ID,进入排行榜的道具最小值,排行榜结算时间,保底触发数量,保底触发条件,触发保底后将抽到的ID,保顶触发数量,保顶触发条件,触发保顶后将抽到的ID,大奖id,大奖单日最多触发次数,大奖出现的时间段,排行奖励邮件,奖励邮件,累计奖励邮件
ID,CostItem,RewardItem,RankItemID,RankMinNum,RankEndTime,EnsureDownNum,EnsureDownCondition,EnsurDownID,EnsureUpNum,EnsureUpCondition,EnsurUpID,GrandPrizeID,GrandPrizeNum,GrandPrizeTime,RankMailID,RewardMailId,TotalRewardMailI
range(0:n),,,,,,range(0:n),range(0:n),,range(0:n),range(0:n),,,,,,,
int,vector:SItem[int:type;int:id;int:count],vector:SItem[int:type;int:id;int:count],class:SItem[int:type;int:id;int:count],int,int,int,vector:int[],int,int,vector:int[],int,vector:int[],int,vector:int[],int,int,int
90500,1:1000413:1;,,1:1000414:1;,500,320400,14,9050007;9050010,9050005,2,9050001;9050006,9050008,9050001,1,10;23,1165,1168,1169
