Id,怪物阵容,BOSS结束时间 以活动开启时间为基准往后推X秒,每日次数上限,每日购买次数上限,次数购买价格,热点时间(新）,热点时间(废弃),热点时间积分加成(万分比),战斗场景,胜利条件,排行奖励最大发放名次,单次积分最大奖励名次,累计积分最大奖励名次,积分公式参数 A;B;C;基础积分
Id,Monster,BossTiming,ChallengeNumMax,BuyNumMax,BuyNumPrice,HotTimeDay,HotTime,HotBuff,SceneId,WinType,RankNum,RankRewardNum1,RankRewardNum2,Parameter
,,,,,,,,,,,,,,
int,int,int,int,int,vector:SItem[int:type;int:id;int:count],string,vector:SVehicleTime[int:day;int:hotstart;int:hotend],int,int,int,int,int,int,string
100001,2900001,432000,3,5,1:1020002:30,25200;32400;46800;57600;,3:10:22,3000,1017,1001,2000,50,50,1000;30;5000000;50;
100002,2900002,432000,3,5,1:1020002:30,25200;32400;46800;57600;,3:10:22,3000,1017,1001,2000,50,50,1000;30;5000000;50;
100003,2900003,432000,3,5,1:1020002:30,25200;32400;46800;57600;,3:10:22,3000,1017,1001,2000,50,50,1000;30;5000000;50;
