活动ID,关联系统Id,邀请者等级下限 （大于等于该等级可邀请他人）,被邀请者等级上限 （小于等级该等级可被邀请）,被邀请奖励,转盘轮次上限,单次抽奖所需积分,当期统计道具,转盘大奖保底次数
ID,SystemBaseId,InviteeLevelMin,InviteesLevelMax,InviteesReward,TurntableReset,TurntableCostNum,KeyItem,TurntableLine
,,,,,,,,
int,int,int,int,vector:SItem[int:type;int:id;int:count],int,class:SItem[int:type;int:id;int:count],class:SItem[int:type;int:id],int
8090001,80900,40,30,2:5010003:100,3,1:1020002:1000,1:80100101,11
