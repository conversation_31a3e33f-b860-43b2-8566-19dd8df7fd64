ID,格斗家,序号,技能解锁,条件类型 1=指定类型（战魄） 2=指定ID（战魄）,条件参数,战魄技能成长星级 ,技能解锁属性 SkillUpGrade
ID,HeroID,Pos,SkillUnlock,ConditionTpye,ConditionValue,HeroSpiritArmedSkill,SkillUnlockAttr
range(0:n),range(0:n),range(0:n),,,,,
int,int,int,int,vector:int[],vector:int[],int,vector:int[]
100401,1004,1,1,1,2,10041,301320011;301334241;301334246;
100402,1004,2,1,1,2,10042,301320011;301334241;301334246;
100403,1004,3,1,1,2,10043,301320011;301334241;301334246;
100404,1004,0,1,1;1;2,2;2;100401,10044,301320011;301334241;301334246;
101301,1013,1,1,1,2,10131,0
101302,1013,2,1,1,2,10132,0
101303,1013,3,1,1,2,10133,0
101304,1013,0,1,1;1;2,2;2;101301,10134,0
130101,1301,1,1,1,2,1301011,301320011;301334241;301334246;
130102,1301,2,1,1,2,1301021,301320011;301334241;301334246;
130103,1301,3,1,1,2,1301031,301320011;301334241;301334246;
130104,1301,0,1,1;1;2,2;2;130101,1301041,301320011;301334241;301334246;
106201,1062,1,1,1,2,1062011,301320011;301334241;301334246;
106202,1062,2,1,1,2,1062021,301320011;301334241;301334246;
106203,1062,3,1,1,2,1062031,301320011;301334241;301334246;
106204,1062,0,1,1;1;2,2;2;106201,1062041,301320011;301334241;301334246;
102401,1024,1,1,1,2,10241,301320011;301334241;301334246;
102402,1024,2,1,1,2,10242,301320011;301334241;301334246;
102403,1024,3,1,1,2,10243,301320011;301334241;301334246;
102404,1024,0,1,1;1;2,2;2;102401,10244,301320011;301334241;301334246;
104201,1042,1,1,1,2,1042011,301320011;301334241;301334246;
104202,1042,2,1,1,2,1042021,301320011;301334241;301334246;
104203,1042,3,1,1,2,1042031,301320011;301334241;301334246;
104204,1042,0,1,1;1;2,2;2;104201,1042041,301320011;301334241;301334246;
104801,1048,1,1,1,2,10481,
104802,1048,2,1,1,2,10483,
104803,1048,3,1,1,2,10482,
104804,1048,0,1,1;1;2,2;2;104801,10484,
