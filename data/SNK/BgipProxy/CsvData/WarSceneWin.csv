主键ID,己方出手次数上限,达到回合上限是否判输,怪物击杀上限,怪物击杀有效怪物集合,血量低于总血量百分比即为胜利,总是胜利,我方指定角色阵亡即为失败,规定回合数内取胜,剩余血量不低于总血量百分比即为胜利,我方战败人数不超过规定数即为胜利
ID,ActiveMax,IsLoseWhenMaxRound,MonsterKilledMax,MonsterKilledSet,RemnantHp,AlwaysWin,HeroDiedSet,MaxRoundWin,MyRemnantHp,LifeHero
,,,,,,,,,,
int,int,int,int,vector:int[],int,int,vector:int[],int,int,int
1000,0,1,0,,0,0,,0,0,0
1001,0,0,0,,0,0,,0,0,0
1002,0,1,16,223111101;224111101;222111101;222211101;222311101;,0,0,,0,0,0
1003,0,0,0,,0,1,,0,0,0
1004,0,1,1,120911201,0,0,,0,0,0
3001,0,1,0,,2000,0,,0,0,0
3002,0,1,0,,9989,0,,0,0,0
3003,0,1,0,,0,1,,0,0,0
4001,0,1,0,,0,0,,5,0,0
4002,0,1,0,,0,0,,4,0,0
4003,0,1,0,,0,0,,0,0,2
4004,0,1,0,,0,0,,0,0,1
4005,0,1,0,,0,0,,0,5000,0
4006,0,1,0,,0,0,,0,3000,0
4007,0,1,0,,0,1,,2,0,0
4008,0,1,0,,0,0,,6,0,0
4009,0,1,0,,0,0,,11,0,0
