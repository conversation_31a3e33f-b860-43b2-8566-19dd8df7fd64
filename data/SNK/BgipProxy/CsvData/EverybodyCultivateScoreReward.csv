ID,所属阶段,序号,分数要求,奖励1,奖励2（皮肤）
ID,PhaseID,Idx,ScoreNeed,Reward1,Reward2
range(0:n),,,,,
int,int,int,int,vector:SItem[int:type;int:id;int:count],vector:SItem[int:type;int:id;int:count]
101,1,1,200,1:450003:3000;,
102,1,2,400,1:450003:3000;,
103,1,3,800,1:1020002:5000;,
104,1,4,1200,1:450003:5000;,
105,1,5,1600,1:450003:5000;,
106,1,6,2000,1:1020102:20;,1:500040:1;
201,2,1,200,1:1020102:5;,
202,2,2,400,1:1020102:5;,
203,2,3,800,1:1020102:10;,
204,2,4,1200,1:1020102:10;,
205,2,5,1600,1:1020102:10;,
206,2,6,2000,1:1020102:20;,1:1000444:1;
