唯一id,大师类型,大师等级,要求条件,解锁属性,要求条件1
ID,Type,Level,Condition,Attribute,Condition1
range(1:n),range(1:n),range(0:n),range(0:n),cfg(SkillUpGrade.csv:ID),range(0:n)
int,int,int,int,vector:int[],int
1001,1,1,10,7000445;7000446;7000447;7000448;,0
1002,1,2,20,7000449;7000450;7000451;7000452;,0
1003,1,3,30,7000453;7000454;7000455;7000456;,0
1004,1,4,40,7000457;7000458;7000459;7000460;,0
1005,1,5,50,7000461;7000462;7000463;7000464;,0
1006,1,6,60,7000465;7000466;7000467;7000468;,0
1007,1,7,70,7000469;7000470;7000471;7000472;,0
1008,1,8,80,7000473;7000474;7000475;7000476;,0
1009,1,9,90,7000477;7000478;7000479;7000480;,0
1010,1,10,100,7000481;7000482;7000483;7000484;,0
1011,1,11,110,7000485;7000486;7000487;7000488;,0
1012,1,12,120,7000489;7000490;7000491;7000492;,0
1013,1,13,130,7000493;7000494;7000495;7000496;,0
1014,1,14,140,7000497;7000498;7000499;7000500;,0
1015,1,15,150,7000501;7000502;7000503;7000504;,0
1016,1,16,160,7000505;7000506;7000507;7000508;,0
1017,1,17,170,7000509;7000510;7000511;7000512;,0
1018,1,18,180,7000513;7000514;7000515;7000516;,0
1019,1,19,190,7000517;7000518;7000519;7000520;,0
1020,1,20,200,7000521;7000522;7000523;7000524;,0
1021,1,21,210,7000525;7000526;7000527;7000528;,0
1022,1,22,220,7000529;7000530;7000531;7000532;,0
1023,1,23,230,7000533;7000534;7000535;7000536;,0
1024,1,24,240,7000537;7000538;7000539;7000540;,0
2001,2,1,1,7000541;7000542;7000543;7000544;,0
2002,2,2,2,7000545;7000546;7000547;7000548;,0
2003,2,3,3,7000549;7000550;7000551;7000552;,0
2004,2,4,4,7000553;7000554;7000555;7000556;,0
2005,2,5,4,7000557;7000558;7000559;7000560;,1
2006,2,6,4,7000561;7000562;7000563;7000564;,2
2007,2,7,5,7000565;7000566;7000567;7000568;,0
2008,2,8,5,7000569;7000570;7000571;7000572;,1
2009,2,9,5,7000573;7000574;7000575;7000576;,2
2010,2,10,5,7000577;7000578;7000579;7000580;,3
3001,3,1,2,7000581;7000582;7000583;7000584;,0
3002,3,2,4,7000585;7000586;7000587;7000588;,0
3003,3,3,6,7000589;7000590;7000591;7000592;,0
3004,3,4,8,7000593;7000594;7000595;7000596;,0
3005,3,5,10,7000597;7000598;7000599;7000600;,0
3006,3,6,12,7000601;7000602;7000603;7000604;,0
3007,3,7,14,7000605;7000606;7000607;7000608;,0
3008,3,8,16,7000609;7000610;7000611;7000612;,0
3009,3,9,18,7000613;7000614;7000615;7000616;,0
3010,3,10,20,7000617;7000618;7000619;7000620;,0
3011,3,11,22,7000621;7000622;7000623;7000624;,0
3012,3,12,24,7000625;7000626;7000627;7000628;,0
3013,3,13,26,7000629;7000630;7000631;7000632;,0
3014,3,14,28,7000633;7000634;7000635;7000636;,0
3015,3,15,30,7000637;7000638;7000639;7000640;,0
4001,4,1,1,7000641;7000642;7000643;7000644;,1
4002,4,2,2,7000645;7000646;7000647;7000648;,0
4003,4,3,3,7000649;7000650;7000651;7000652;,0
4004,4,4,4,7000653;7000654;7000655;7000656;,0
4005,4,5,5,7000657;7000658;7000659;7000660;,0
4006,4,6,6,7000661;7000662;7000663;7000664;,0
4007,4,7,7,7000665;7000666;7000667;7000668;,0
4008,4,8,8,7000669;7000670;7000671;7000672;,0
4009,4,9,9,7000673;7000674;7000675;7000676;,0
4010,4,10,10,7000677;7000678;7000679;7000680;,0
4011,4,11,11,7000681;7000682;7000683;7000684;,0
