ID,归属页签,找回种类,找回资源所乘系数,玩法倍率生效次数,找回倍率上限 Tab字段为2时生效
ID,Tab,Type,Rate,ExtraRewardTimes,ComboLimit
range(0:n),range(1:3),range(1:10),,range(0:n),
int,int,int,vector:int[],int,vector:SFindBackCombo[int:day;int:combo]
1,1,1,100;100;100;100;100,0,
2,1,1,100;100;100;100;100,0,
3,1,1,100;100;100;100;100,0,
4,3,1,,0,
5,3,1,,0,
6,2,2,,1,1:1;2:2;3:3;4:4;5:5;
7,2,2,,1,1:1;2:2;3:3;4:4;5:5;
8,2,2,,1,1:1;2:2;3:3;4:4;5:5;
9,2,2,,1,1:1;2:2;3:3;4:4;5:5;
10,2,2,,1,1:1;2:2;3:3;4:4;5:5;
11,2,2,,1,1:1;2:2;3:3;4:4;5:5;
12,2,2,,1,1:1;2:2;3:3;4:4;5:5;
13,2,2,,1,1:1;2:2;3:3;4:4;5:5;
14,2,2,,1,1:1;2:2;3:3;4:4;5:5;
15,2,2,,1,1:1;2:2;3:3;4:4;5:5;
16,2,2,,1,1:1;2:2;3:3;4:4;5:5;
