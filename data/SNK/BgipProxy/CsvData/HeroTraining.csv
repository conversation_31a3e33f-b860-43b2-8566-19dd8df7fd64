试炼ID;主键,角色ID,关卡序号,关卡奖励 类型，ID，数量,关闭站位,强制上阵角色 参数【位置】【角色】角色=MonsterID,试炼角色,助战角色,角色上阵数量限制,怪物组,场景,关卡词缀,关卡胜利条件
DungeonID,HeroID,Num,Re<PERSON>,BanPos,ForceHero,MainHeroID,MateList,HeroNumLimit,MonsterGroup,SceneStencilID,DungeonBuff,WarSceneWin
range(0:n),cfg(Hero.csv:ID),range(0:n),,range(0:6),range(0:6)|cfg(Monster.csv:ID),cfg(Monster.csv:ID),cfg(Monster.csv:ID),range(0:6),cfg(MonsterGroup.csv:ID),cfg(SceneStencil.csv:ID),cfg(SkillAffixes.csv:ID),cfg(WarSceneWin.csv:ID)
int,int,int,class:SItem[int:type;int:id;int:count],vector:int[],vector:SForceHero[int:pos;int:monsterid],int,vector:int[],int,int,int,vector:int[],int
106201,1062,1,1:1020002:50,,5:301062101;,301062101,301003101;301013101;301024101;,3,30106201,1027,30106201,1000
106202,1062,2,1:1020002:50,,5:301062201;,301062201,301003201;301014201;301070201;,3,30106202,1027,30106202,1000
106203,1062,3,1:1020002:50,,5:301062301;,301062301,301222301;301310301;301060301;301009301;301007301;301014301,6,30106203,1027,30106203,1000
100201,1002,1,1:1020002:50,,4:301002101;,301002101,301003102;301062102;301063101;,2,30100201,1027,30100201,1000
100202,1002,2,1:1020002:50,,1:301002201;,301002201,301003202;301046201;301005201;301006201,3,30100202,1027,30100202,1000
100203,1002,3,1:1020002:50,,5:301002301;,301002301,301031301;301033301;301043301;301303301;301301301;,6,30100203,1027,30100203,1000
100301,1003,1,1:1020002:50,,2:301003101;,301003101,301003103;301002102;301062103;301060101;,3,30100301,1027,30100301,1000
100302,1003,2,1:1020002:50,,2:301003301;,301003301,301310302;301062302;301007302;301016301;301017301;301014302,6,30100302,1027,30100302,1000
100701,1007,1,1:1020002:50,,4:301007001;,301007001,301008001;301063001;,3,30100701,1027,30100701,1000
100702,1007,2,1:1020002:50,,1:301007002;,301007002,301017002;301016002;301004002;,4,30100702,1027,30100702,1000
100703,1007,3,1:1020002:50,,1:301007003;,301007003,301310003;301016003;301023003;301041003;301040003;,6,30100703,1027,30100703,1000
103101,1031,1,1:1020002:50,,1:301031001;,301031001,301033001;,2,30103101,1027,30103101,1000
103102,1031,2,1:1020002:50,,5:301031002;,301031002,301033002;301209002;301203002;,4,30103102,1027,30103102,1000
103103,1031,3,1:1020002:50,,3:301031003;,301031003,301033003;301048003;301044003;301068003;301041003;,6,30103103,1027,30103103,1000
103301,1033,1,1:1020002:50,,1:301033001;,301033001,301032001;301031001;,3,30103301,1027,30103301,1000
103302,1033,2,1:1020002:50,,1:301033002;,301033002,301301002;301303002;301202002;,4,30103302,1027,30103302,1000
103303,1033,3,1:1020002:50,,2:301033003;,301033003,301044003;301301003;301303003;301031003;301043003;,6,30103303,1027,30103303,1000
100401,1004,1,1:1020002:50,,2:301004001;,301004001,301005001;301006001;,3,30100401,1027,30100401,1000
100402,1004,2,1:1020002:50,,2:301004002;,301004002,301016002;301007002;,3,30100402,1027,30100402,1000
100403,1004,3,1:1020002:50,,6:301004003;,301004003,301044003;301042003;301043003;301016003;301007003;,6,30100403,1027,30100403,1000
100501,1005,1,1:1020002:50,,2:301005001;,301005001,301006001;,2,30100501,1027,30100501,1000
100502,1005,2,1:1020002:50,,1:301005002;,301005002,301006002;301004002;,3,30100502,1027,30100502,1000
100503,1005,3,1:1020002:50,,6:301005003;,301005003,301007003;301006003;301008003;301023003;301018003;,6,30100503,1027,30100503,1000
100601,1006,1,1:1020002:50,,2:301006001;,301006001,301008001;301005001;,3,30100601,1027,30100601,1000
100602,1006,2,1:1020002:50,,4:301006002;,301006002,301018002;301005002;,3,30100602,1027,30100602,1000
100603,1006,3,1:1020002:50,,5:301006003;,301006003,301310003;301040003;301041003;301023003;301005003;,6,30100603,1027,30100603,1000
120101,1201,1,1:1020002:50,,2:301201001;,301201001,301205001;301015001;,3,30120101,1027,30120101,1000
120102,1201,2,1:1020002:50,,2:301201002;,301201002,301302002;301205002;301204002;,4,30120102,1027,30120102,1000
120103,1201,3,1:1020002:50,,2:301201003;,301201003,301302003;301205003;301210003;301202003;301204003;,6,30120103,1027,30120103,1000
120201,1202,1,1:1020002:50,,2:301202001;,301202001,301015001;301222001;,3,30120201,1027,30120201,1000
120202,1202,2,1:1020002:50,,5:301202002;,301202002,301302002;301204002;301201002;,4,30120202,1027,30120202,1000
120203,1202,3,1:1020002:50,,2:301202003;,301202003,301222003;301205003;301210003;301201003;301204003;,6,30120203,1027,30120203,1000
120301,1203,1,1:1020002:50,,1:301203001;,301203001,301209001;,2,30120301,1027,30120301,1000
120302,1203,2,1:1020002:50,,2:301203002;,301203002,301209002;301043002;301044002;,4,30120302,1027,30120302,1000
120303,1203,3,1:1020002:50,,3:301203003;,301203003,301209003;301044003;301301003;301304003;301043003;,6,30120303,1027,30120303,1000
102301,1023,1,1:1020002:50,,4:301023001;,301023001,301009001;301008001;,3,30102301,1027,30102301,1000
102302,1023,2,1:1020002:50,,2:301023002;,301023002,301007002;301008002;301063002;,4,30102302,1027,30102302,1000
102303,1023,3,1:1020002:50,,5:301023003;,301023003,301007003;301041003;301008003;301009003;301018003;,6,30102303,1027,30102303,1000
104001,1040,1,1:1020002:50,,3:301040101;,301040101,301040102;,2,30104001,1027,30104001,1000
104002,1040,2,1:1020002:50,,3:301040201;,301040201,301040202;301040203;301040204;,4,30104002,1027,30104002,1000
104003,1040,3,1:1020002:50,,2:301040301;,301040301,301040302;301040303;301040304;301040305;301040306,6,30104003,1027,30104003,1000
104101,1041,1,1:1020002:50,,2:301041102;,301041102,301041101;301041103,3,30104101,1027,30104101,1000
104102,1041,2,1:1020002:50,,1:301041202;,301041202,301041201;301041203;301041204,4,30104102,1027,30104102,1000
104103,1041,3,1:1020002:50,,5:301041304;,301041304,301041303;301041305;301041306;301041307;301041308,6,30104103,1027,30104103,1000
104401,1044,1,1:1020002:50,,4:301044101;,301044101,301044102;301044103,3,30104401,1027,30104401,1000
104402,1044,2,1:1020002:50,,5:301044201;,301044201,301044202;301044203;301044204,4,30104402,1027,30104402,1000
104403,1044,3,1:1020002:50,,5:301044302;,301044302,301044303;301044304;301044305;301044306;301044307,6,30104403,1027,30104403,1000
