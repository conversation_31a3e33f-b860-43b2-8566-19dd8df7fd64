索引ID,段位差距,到达是否广播,赛季结束重置到的段位,失败是否扣积分,段位场景,几场不会匹配同一对手,只匹配机器人,相应的升段积分,可以匹配的积分下限,可以匹配的积分上限,积分下限扩大步长,积分上限扩大步长,附加积分参数,附加积分参数
ID,DuanDiff,Isbroadcast,DuanResetRule,LoseToStar,SceneID,RepeatMatchCD,OnlyMatchBot,Fighting3SRankScore,MatchDuanLow,MatchDuanHigh,MatchScoreLowStep,MatchScoreHighStep,Fighting3SArg,Fighting3SArgC
range(0:n),,range(0:n),,,cfg(SceneStencil.csv:ID),range(0:n),range(0:n),,,,,,,
int,int,int,int,bool,int,int,int,int,int,int,vector:int[],vector:int[],int,int
1,1,0,1,1,1027,0,1,1000,1000,1350,25;50;75,0;0;10,3,0
2,1,0,1,1,1027,0,1,1200,1000,1550,25;50;75,0;0;10,4,0
3,1,0,1,1,1027,0,0,1400,1050,1750,25;50;75,0;0;10,5,0
4,1,0,1,1,1027,0,0,1600,1250,1950,25;50;75,0;0;10,6,0
5,1,0,1,1,1027,0,0,1700,1350,2050,25;50;75,0;0;10,6,0
6,1,0,1,1,1027,0,0,1800,1450,2150,25;50;75,0;0;10,7,0
7,1,0,1,1,1027,0,0,1900,1550,2250,25;50;75,0;0;10,7,0
8,1,0,2,1,1027,0,0,2000,1650,2350,25;50;75,0;0;10,8,0
9,1,0,2,1,1027,0,0,2100,1750,2450,25;50;75,0;0;10,8,0
10,1,0,2,1,1027,0,0,2200,1850,2550,25;50;75,0;0;10,9,0
11,1,0,2,1,1027,0,0,2300,1950,2650,25;50;75,0;0;10,9,0
12,1,0,3,1,1027,0,0,2400,2050,2750,75;100;125,0;0;25,10,0
13,1,0,3,1,1027,0,0,2500,2150,2850,75;100;125,0;0;25,10,0
14,1,0,3,1,1027,0,0,2600,2250,2950,75;100;125,0;0;25,10,0
15,1,0,3,1,1027,0,0,2700,2350,3050,75;100;125,0;0;25,10,0
16,1,0,4,1,1027,0,0,2800,2450,3150,75;100;125,0;0;25,10,0
