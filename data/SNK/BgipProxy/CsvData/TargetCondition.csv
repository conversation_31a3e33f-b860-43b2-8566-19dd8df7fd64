索引ID,筛选类型(二次条件筛选生效目标),是否群体目标,是否包含死亡单位
ID,SpecialSelect,IsRangeDmg,IsDead
range(0:n),range(1:21)|none|none|none|none,select(0:1),select(0:1:2)
int,vector:SSpecial[int:id;string:p1;string:p2;string:p3;string:p4],int,int
1,,1,0
2,8:1:0:0:0;,1,0
3,9:0:0:0:0;,0,0
4,7:0:0:0:0;,1,0
5,8:0:0:0:0;,0,0
6,21:column:0:3:column;,1,0
7,21:line:0:3:line;,1,0
8,21:column:0:3:column;21:line:5:3:line;,0,0
9,8:0:0:0:0;,0,1
10,9:1:0:0:0;,1,0
11,11:0:0:0:0;,0,0
12,,1,2
14,8:0:0:0:0;10:0:0:0:0;,0,0
15,6:0:0:0:0;,1,0
16,9:0:0:0:0;10:0:0:0:0;,1,0
17,18:line:0:1:line;,1,0
18,18:column:0:1:column;,1,0
19,18:sex:0:0:2;,1,0
20,18:sex:0:0:1;,1,0
21,18:column:0:0:1;,1,0
22,18:column:0:0:2;,1,0
23,18:column:0:0:3;,1,0
24,18:line:0:0:1;,1,0
25,18:line:0:0:2;,1,0
31,18:position:0:0:0;,0,0
32,18:position:0:0:1;,0,0
33,18:position:0:0:2;,0,0
34,18:position:0:0:3;,0,0
35,18:position:0:0:4;,0,0
36,18:position:0:0:5;,0,0
37,18:position:0:0:6;,0,1
38,18:position:0:0:7;,0,1
39,3:1:1:maxhp:0;,0,1
40,18:strategy_camp:0:0:1;,1,0
41,18:strategy_camp:0:0:2;,1,0
42,18:strategy_camp:0:0:3;,1,0
43,18:strategy_camp:0:0:4;,1,0
44,3:1:0:maxhp:0;,0,1
117,18:line:0:1:line;8:1:0:0:0;,1,0
118,18:column:0:1:column;8:1:0:0:0;,1,0
201,5:1:0:0:0;,0,0
202,5:2:0:0:0;,1,0
203,5:3:0:0:0;,1,0
204,5:4:0:0:0;,1,0
205,5:5:0:0:0;,1,0
210,18:occupation:0:0:1;,1,0
212,18:occupation:0:0:1;5:2:0:0:0;,1,0
220,18:occupation:0:0:2;,1,0
222,18:occupation:0:0:2;5:2:0:0:0;,1,0
230,18:occupation:0:0:3;,1,0
232,18:occupation:0:0:3;5:2:0:0:0;,1,0
240,18:occupation:0:0:4;,1,0
242,18:occupation:0:0:4;5:2:0:0:0;,1,0
243,18:occupation:0:0:4;8:1:0:0:0;,1,0
1011,3:1:1:attack:0;,0,0
1012,3:1:0:attack:0;,0,0
1013,8:1:0:0:0;3:1:1:attack:0;,0,0
1021,2:1:1:0:0;,0,0
1022,2:1:0:0:0;,0,0
1024,8:1:0:0:0;2:1:0:0:0;,0,0
1025,8:1:0:0:0;19:1:1:23:0;2:1:0:0:0;,0,0
1026,3:1:1:curhp:0;,0,0
1031,3:1:1:position:0;,0,0
1032,3:1:0:position:0;,0,0
1033,21:line:0:1032:line;,1,0
1034,21:line:0:1031:line;,1,0
1035,18:line:0:0:1;2:1:0:0:0;,0,0
1036,18:line:0:0:2;2:1:0:0:0;,0,0
1037,2:2:0:0:0;,0,0
1038,21:line:0:1032:line;2:1:0:0:0;,1,0
103801,21:column:0:1038:column;21:line:5:3:line;,1,0
1039,3:2:1:attack:0;,0,0
1040,21:column:0:1042:column;3:1:0:position:0;,0,0
1041,21:column:0:1042:column;3:1:1:position:0;,0,0
1042,19:0:0:34009110:0;,0,0
1043,21:column:0:1042:column;,1,0
1044,21:line:0:1031:line;3:1:1:attack:0;,1,0
1110,18:line:0:0:1;18:strategy_camp:5:0:1;,1,0
1111,18:line:0:0:1;18:strategy_camp:0:0:1;,1,0
1210,18:line:0:0:2;18:strategy_camp:5:0:1;,1,0
1211,18:line:0:0:2;18:strategy_camp:0:0:1;,1,0
1120,18:line:0:0:1;18:strategy_camp:5:0:2;,1,0
1121,18:line:0:0:1;18:strategy_camp:0:0:2;,1,0
1220,18:line:0:0:2;18:strategy_camp:5:0:2;,1,0
1221,18:line:0:0:2;18:strategy_camp:0:0:2;,1,0
21002,15:1002:0:0:0;,0,0
21003,15:1003:0:0:0;,0,0
21004,15:1004:0:0:0;,0,0
21005,15:1005:0:0:0;,0,0
21006,15:1006:0:0:0;,0,0
21007,15:1007:0:0:0;,0,0
21008,15:1008:0:0:0;,0,0
21009,15:1009:0:0:0;,0,0
21011,15:1011:0:0:0;,0,0
21012,15:1012:0:0:0;,0,0
21013,15:1013:0:0:0;,0,0
21014,15:1014:0:0:0;,0,0
21015,15:1015:0:0:0;,0,0
21016,15:1016:0:0:0;,0,0
21017,15:1017:0:0:0;,0,0
21018,15:1018:0:0:0;,0,0
21019,15:1019:0:0:0;,0,0
21020,15:1020:0:0:0;,0,0
21021,15:1021:0:0:0;,0,0
21031,15:1031:0:0:0;,0,0
21032,15:1032:0:0:0;,0,0
21033,15:1033:0:0:0;,0,0
21034,15:1034:0:0:0;,0,0
21036,15:1036:0:0:0;,0,0
21037,15:1037:0:0:0;,0,0
21038,15:1038:0:0:0;,0,0
210381,15:1038:0:0:0;,0,1
21040,15:1040:0:0:0;,0,0
21041,15:1041:0:0:0;,0,0
21042,15:1042:0:0:0;,0,0
21043,15:1043:0:0:0;,0,0
21044,15:1044:0:0:0;,0,0
21048,15:1048:0:0:0;,0,0
21060,15:1060:0:0:0;,0,0
21062,15:1062:0:0:0;,0,0
21063,15:1063:0:0:0;,0,0
21069,15:1069:0:0:0;,0,0
21070,15:1070:0:0:0;,0,0
21071,15:1071:0:0:0;,0,0
21201,15:1201:0:0:0;,0,0
21202,15:1202:0:0:0;,0,0
21203,15:1203:0:0:0;,0,0
21204,15:1204:0:0:0;,0,0
21205,15:1205:0:0:0;,0,0
21215,15:1215:0:0:0;,0,0
21222,15:1222:0:0:0;,0,0
21301,15:1301:0:0:0;,0,0
21302,15:1302:0:0:0;,0,0
21303,15:1303:0:0:0;,0,0
21401,15:1401:0:0:0;,0,0
15001,21:line:0:3:line;21:column:5:3:column;,1,0
15002,8:1:0:0:0;18:state:0:0:7;,1,0
15003,19:0:0:31302252:0;,1,0
15004,21:column:0:5:column;21:line:5:5:line;,1,0
15005,19:0:0:31205311:0;,1,0
15006,2:1:0:0:0;10:0:0:0:0;,1,0
15007,8:0:0:0:0;10:0:0:0:0;19:0:1:31024311:0;,1,0
15008,21:column:0:5:column;,1,0
15009,19:0:0:31048511:0;,1,0
15010,19:0:0:31209231:0;,1,0
15011,3:1:0:fury:0;,1,0
15012,3:1:1:attack:0;10:0:0:0:0;,1,0
13043,7:0:0:0:0;18:sex:0:0:2;,1,0
10631,19:0:0:31063412:0;,0,0
10632,19:0:0:31063311:0;,0,2
10381,3:1:1:attack:0;,0,0
10382,5:1:0:0:0;,0,0
10383,7:0:0:0:0;,1,0
10384,18:state:0:0:7,1,0
10385,19:0:0:31038451:0;18:shield:0:0:0;,0,0
10386,7:0:0:0:0;,0,0
10388,3:2:1:attack:0;,0,0
10387,18:shield:1:0:0;,1,0
10091,8:1:0:0:0;,0,0
10094,3:1:1:fury:0;,0,0
10095,8:1:0:0:0;3:1:1:fury:0;,0,0
10096,3:2:1:fury:0;,0,0
10342,19:0:0:31034211:0;,0,2
10343,19:0:1:31034411:0;19:0:1:31034213:0;,0,2
10191,7:0:0:0:0;,1,0
10097,8:1:0:0:0;3:2:1:fury:0;,1,0
16001,18:tmp_para108:4:0:3000;,1,0
16002,18:tmp_para108:4:0:3000;18:sex:0:0:1;19:0:1:31007214:0;,1,0
16003,18:tmp_para109:0:0:1;,1,0
16004,5:1:0:0:0;,0,2
16006,19:1:0:9001:0;,1,1
16007,19:1:0:9002:0;19:1:1:9001:0;,0,0
16008,19:0:0:31024311:0;18:state:0:0:7;,1,0
16009,8:0:0:0:0;10:0:0:0:0;,1,0
13044,19:0:0:31043414:0;,0,0
13045,18:use_skill:5:0:3;,0,0
13046,18:use_skill:0:0:3;,0,0
12041,19:0:0:31204434:0;,0,0
16010,19:0:0:31042311:0;7:0:0:0:0;10:0:0:0:0;,0,0
16011,18:use_skill:1:0:0;,0,0
16012,18:line:0:0:1;5:1:0:0:0;,0,0
16013,3:2:1:attack:0;,0,0
16014,19:0:0:31033311:0;7:0:0:0:0;,0,0
16015,19:0:0:31033312:0;7:0:0:0:0;,0,0
12221,19:1:0:2:0;5:1:0:0:0;,1,0
16017,7:0:0:0:0;2:1:1:0:0;,0,0
16018,19:0:0:31017312:0;,0,0
16019,19:0:0:31017111:0;,1,0
16020,21:line:0:16018:line;,1,0
12151,19:0:0:31215310:0;,0,0
12152,19:0:0:31215311:0;,0,0
10401,19:0:1:20104001:0;3:1:0:position:0;,0,0
16021,18:line:0:0:2;18:sex:0:0:2;,1,0
10361,19:0:1:30103663:0;3:1:0:position:0;,0,0
20471,18:tmp_para108:4:0:3500;,1,0
20472,18:tmp_para108:2:0:7000;,1,0
3120501,21:line:0:1032:line;19:0:0:90611:0;18:fury:2:0:100;,1,0
3120502,21:line:0:1032:line;19:0:0:90611:0;18:fury:3:0:100;,1,0
10362,19:0:0:30103665:0;3:1:0:position:0;,0,0
10363,19:0:1:30103663:0;3:1:0:position:0;,0,0
103824,19:0:0:31038241:0;,0,0
10192,8:1:0:0:0;,1,1
16022,19:0:0:31042312:0;,0,0
101501,19:0:0:31015304:0;,0,0
16023,21:column:0:1021:column;,1,0
121001,21:column:0:1011:column;,1,0
172201,21:column:0:10382:column;,1,0
102301,18:line:0:0:1;18:sex:0:0:2;,1,0
102302,18:line:0:0:2;18:sex:0:0:2;,1,0
10341,9:0:0:0:0;10:0:0:0:0;21:column:5:3:column;,1,0
50031,19:0:0:50000312:0;10:0:0:0:0;,1,0
50032,19:0:0:50000312:0;10:0:0:0:0;,1,0
50033,19:1:0:50000312:0;,1,0
50022,7:0:0:0:0;18:shield:0:0:0;19:0:0:50000223:0;,1,0
50051,21:column:0:1022:column;,1,0
331041,18:line:0:0:2;2:1:0:0:0;21:column:0:3:column;,1,0
3310331,19:0:0:331033313:0;,1,0
3312091,19:0:1:31209251:0;,1,0
310911,9:0:0:0:0;21:column:0:3:column;18:line:0:0:1;,1,0
310912,9:0:0:0:0;21:column:0:3:column;18:line:0:0:2;,1,0
102401,19:0:0:31024621:0;3:1:0:position:0;,0,0
102201,18:position:0:1:position;,0,0
102202,21:position:0:102201:position;,0,0
102203,21:position:0:102201:position;10:0:0:0:0;,1,0
102204,21:line:0:1031:line;2:1:0:0:0;,0,0
102205,21:line:0:1032:line;2:1:1:0:0;,0,0
102206,18:column:0:1:column;18:line:0:0:1;,0,0
102207,18:column:0:1:column;18:line:0:0:2;,0,0
102208,18:line:0:0:1;5:1:0:0:0;,1,0
102209,18:line:0:0:2;5:1:0:0:0;,1,0
102210,19:1:0:31022231:0;18:column:0:1:column;5:1:0:0:0;,0,0
102211,19:1:0:31022231:0;5:1:0:0:0;,0,0
102212,3:1:1:position:0;10:0:0:0:0;,0,0
120601,19:0:0:31206271:0;3:1:0:position:0;,0,0
120602,21:column:0:31:column;21:line:5:31:line;,0,0
120603,21:column:0:32:column;21:line:5:32:line;,0,0
120604,21:column:0:33:column;21:line:5:33:line;,0,0
120605,18:line:0:0:2;3:1:0:position:0;,0,0
172301,19:0:0:31723611:0;3:1:0:position:0;,0,0
172302,21:line:0:1032:line;19:0:0:31723611:0;3:1:0:position:0;,0,0
120801,19:0:0:31208222:0;,0,0
120802,19:0:0:31208222:0;10:0:0:0:0;,0,0
120803,,1,1
104501,19:0:0:31045501:0;,0,0
108801,19:0:0:31088322:0;3:1:0:position:0;,0,0
108802,3:1:1:fury:0;3:1:0:position:0;,0,0
140201,21:line:0:1031:line;5:1:0:0:0;,0,0
140202,19:0:0:31402231:0;,0,0
