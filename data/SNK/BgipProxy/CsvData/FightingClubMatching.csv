索引ID,赛季ID,积分最小 匹配方,积分最大 匹配方,胜场数,几场不会 匹配同一对手,可匹配的目标胜场次数-下限,可匹配的目标胜场次数-上限,可以匹配积分差值 小于,可以匹配积分差值 大于,积分下限扩大步长,积分上限扩大步长,匹配机器人概率,无有效目标，强制匹配对手 0=机器人补位 1=本服任意玩家补位
ID,SeasonID,ScoreMin,ScoreMax,WinsNum,RepeatMatchCD,MatchWinsMin,MatchWinsMax,MatchScoreMin,MatchScoreMax,MatchScoreLowStep,MatchScoreHighStep,MatchBotWeight,TargetForce
range(0:n),range(0:n),range(0:n),range(0:n),range(0:n),range(0:n),range(0:n),range(0:n),range(0:n),range(0:n),,,range(0:n),range(0:n)
int,int,int,int,int,int,int,int,int,int,vector:int[],vector:int[],int,int
1,1,0,50,1,2,0,3,100,200,25;50;75,0;0;50,100,0
2,1,51,100,1,2,0,3,150,200,25;50;75,0;0;100,100,0
3,1,101,200,1,2,0,3,150,200,25;50;75,0;0;100,0,0
4,1,201,400,1,2,0,3,150,200,25;50;100,0;0;100,0,0
5,1,401,600,1,2,0,3,150,200,25;50;200,0;0;200,0,1
6,1,601,700,1,2,0,3,200,200,25;50;300,0;0;200,0,1
7,1,701,800,1,2,0,3,200,200,25;50;350,0;0;200,0,1
8,1,801,900,1,2,0,3,200,200,25;50;400,0;0;200,0,1
9,1,901,1000,1,2,0,3,200,200,25;50;450,0;0;200,0,1
10,1,1001,1500,1,2,0,3,300,500,25;50;500,0;0;300,0,1
11,1,1501,2000,1,2,0,3,300,500,25;50;500,0;0;300,0,1
12,1,2001,2500,1,2,0,3,400,500,25;50;500,0;0;300,0,1
13,1,2501,3000,1,2,0,3,400,500,25;50;500,20;30;400,0,1
14,1,3001,3500,1,2,0,3,500,500,25;50;500,20;30;400,0,1
15,1,3501,4000,1,2,0,3,500,500,25;50;500,20;30;400,0,1
16,1,4001,4500,1,2,0,3,1000,99999,25;50;500,20;30;400,0,1
17,1,4501,5000,1,2,0,3,1000,99999,25;50;500,20;30;400,0,1
18,1,5001,5500,1,2,0,3,2000,99999,25;50;500,20;30;600,0,1
19,1,5501,6000,1,2,0,3,2000,99999,25;50;500,20;30;600,0,1
20,1,6001,6500,1,2,0,3,2000,99999,25;50;500,20;30;600,0,1
21,1,6501,7000,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
22,1,7001,7500,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
23,1,7501,8000,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
24,1,8001,8500,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
25,1,8501,9000,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
26,1,9001,9500,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
27,1,9501,999999,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
28,2,0,50,1,2,0,3,100,200,25;50;75,0;0;50,100,0
29,2,51,100,1,2,0,3,150,200,25;50;75,0;0;100,0,0
30,2,101,200,1,2,0,3,150,200,25;50;75,0;0;100,0,0
31,2,201,400,1,2,0,3,150,200,25;50;100,0;0;100,0,0
32,2,401,600,1,2,0,3,150,200,25;50;200,0;0;200,0,1
33,2,601,700,1,2,0,3,200,200,25;50;300,0;0;200,0,1
34,2,701,800,1,2,0,3,200,200,25;50;350,0;0;200,0,1
35,2,801,900,1,2,0,3,200,200,25;50;400,0;0;200,0,1
36,2,901,1000,1,2,0,3,200,200,25;50;450,0;0;200,0,1
37,2,1001,1500,1,2,0,3,300,500,25;50;500,0;0;300,0,1
38,2,1501,2000,1,2,0,3,300,500,25;50;500,0;0;300,0,1
39,2,2001,2500,1,2,0,3,400,500,25;50;500,0;0;300,0,1
40,2,2501,3000,1,2,0,3,400,500,25;50;500,20;30;400,0,1
41,2,3001,3500,1,2,0,3,500,500,25;50;500,20;30;400,0,1
42,2,3501,4000,1,2,0,3,500,500,25;50;500,20;30;400,0,1
43,2,4001,4500,1,2,0,3,1000,99999,25;50;500,20;30;400,0,1
44,2,4501,5000,1,2,0,3,1000,99999,25;50;500,20;30;400,0,1
45,2,5001,5500,1,2,0,3,2000,99999,25;50;500,20;30;600,0,1
46,2,5501,6000,1,2,0,3,2000,99999,25;50;500,20;30;600,0,1
47,2,6001,6500,1,2,0,3,2000,99999,25;50;500,20;30;600,0,1
48,2,6501,7000,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
49,2,7001,7500,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
50,2,7501,8000,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
51,2,8001,8500,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
52,2,8501,9000,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
53,2,9001,9500,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
54,2,9501,999999,1,2,0,3,4000,99999,25;50;500,20;30;600,0,1
