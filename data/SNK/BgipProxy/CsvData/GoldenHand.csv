ID,对应功能,购买上限,按钮分布,购买内容,消耗道具id,购买道具id,暴击内容,宝箱开关
ID,TrackID,BuyLimit,ButtonType,PriceType,CostID,ResourceID,Bonus,ChestSwitch
range(0:n),range(0:n),range(0:n),range(0:n),,cfg(Item.csv:ID),cfg(Item.csv:ID),range(0:n)|range(0:n),range(0:n)
int,int,int,int,vector:Price[int:id;int:count],int,int,vector:Bonus[int:rate;int:percent],int
1,50503,20,2,1:0;3:10;5:20;6:30;8:40;9:50;10:60;12:80;15:100;20:150;25:200;30:300;,1020002,1020001,2:20;5:2;,1
2,50503,20,2,1:0;3:10;5:20;6:30;8:40;9:50;10:60;12:80;15:100;20:150;25:200;30:300;,1020002,1020006,2:20;,1
