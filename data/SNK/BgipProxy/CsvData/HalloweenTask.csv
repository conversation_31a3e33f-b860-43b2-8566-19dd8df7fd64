TaskID,活动OpenID,SDK商品ID,消耗道具ID,达成条件类型 101 - 登录 102 - 活跃度 103- 消耗道具 104 - 购买道具 105 - 等级,达成条件参数,是否每日刷新,获得奖励,任务前往,活动结束是否发发送未领取奖励
ID,SystemOpenId,SDKGoodsID,CostItemID,ConditionType,ConditionParam,IsDaily,Reward,ToSystem,IsSendTaskReward
,,,,,,,,,
int,int,int,int,int,int,int,vector:SItem[int:type;int:id;int:count],int,int
1,6360001,0,0,101,1,1,1:1000346:2,11500,0
2,6360001,0,0,102,80,1,1:1000346:3,11500,0
3,6360001,0,1020002,103,1000,0,1:1000346:1,70100,1
4,6360001,11001201,0,104,1,0,1:1000346:4,63601,1
