<sfw>
    #分区服务标记，例如snk.zone.101, 如填，调用分区服务时默认调用相同division的服务，全局服务不用填
    #setdivision = 

    #作为Client端连接其他服务时的配置
    <client>
        #注册服务器地址，调用其他未指定具体IP端口的服务时，从注册服务器地址处获得具体连接地址，这里必须填写具体的注册服务器的IP端口
        locator                   = sfw.sfwregistry.QueryObj@tcp -h 127.0.0.1 -p 2000 -t 100000
        #刷新其他服务连接地址Endpoint的时间间隔（毫秒），默认6000（定时刷新连接地址用于剔除不可用连接，增加新的连接）
        refresh-endpoint-interval = 60000
        #同步调用接口的超时时间（毫秒），默认3000， 最小100
        sync-invoke-timeout       = 3000
        #异步调用接口的超时时间（毫秒），默认5000， 最小100
        async-invoke-timeout      = 5000
        #建立连接的超时时间（毫秒），默认1000， 最小100， 最大5000
        connect-timeout           = 1000
        #接受异步调用回调消息的线程个数，默认3
        asyncthread               = 3
    </client>

    #作为Server端时的配置
    <server>
        #app名称，必填
        app      = SNK
        #server名称，必填
        server   = AccountServer
        #本地日志输出路径
        logpath  = /data/applog
        #滚动日志文件按大小拆分， 每个日志文件的最大大小， 默认100M
        #logsize =
        #滚动日志文件最大个数，超过数量则删除最早的的数据， 默认20
        lognum = 100
        #逻辑层日志级别（DEBUG，INFO，ERROR）默认DEBUG
        loglevel = DEBUG
        #SFW框架层日志级别（DEBUG，INFO，ERROR）默认INFO
        #framework-loglevel = INFO
        #主线程主循环间隔（毫秒），默认为0， 代表主线程不进行定时循环
        #loop-interval =
        agent-report = sfw.sfwagent.AgentReportObj@tcp -h 127.0.0.1 -p 2002 -t 3600000

        #做为服务端提供的Service服务， 每个Service开一个端口
        <Service_1>
            #service 名称，必填
            service    = SNK.AccountServer.AccountServiceObj
            #绑定IP端口地址， 格式： 协议（tcp udp unix） -h IP地址 -p 端口 -t 超时时间（超过该时间没有数据请求，则断开链接）
            endpoint   = tcp -h 127.0.0.1 -p 7011 -t 60000
            #协议类型， sfw协议或nosfw
            protocol   = sfw
            #接收该Service消息逻辑处理的Handle线程个数
            threads    = 20
            #该Service最大链接数，超过建立连接失败
            maxconns   = 1024
            #该Service的消息队列长度，超过丢弃，接近时触发过载
            queuecap   = 10240
            #消息未处理超时（毫秒），消息在队列中超过该时间未处理，触发超时机制
            queuetimeout = 5000
        </Service_1>
    </server>
</sfw>
