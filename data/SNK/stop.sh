#!/bin/bash

# SNK Game Server Automatic Stop Script (Fixed)
# Author: Auto-generated for SNK Game Management
# Description: Automatically stops all game servers with fixed paths

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/data/applog/shutdown_auto.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log_message "${GREEN}=== SNK Game Server Automatic Shutdown ===${NC}"

# Stop zone-specific services first (FIXED PATHS)
log_message "Stopping zone-specific services..."

# Stop GameServer (FIXED PATH)
if [ -d "$SCRIPT_DIR/snk.zone.101/GameServer" ]; then
    log_message "Stopping GameServer instances..."
    cd "$SCRIPT_DIR/snk.zone.101/GameServer"
    ./stopall.sh
    sleep 3
else
    log_message "${RED}ERROR: GameServer directory not found${NC}"
fi

# Stop BattleServer (FIXED PATH)
if [ -d "$SCRIPT_DIR/snk.battle.101/BattleServer" ]; then
    log_message "Stopping BattleServer..."
    cd "$SCRIPT_DIR/snk.battle.101/BattleServer"
    ./stop.sh
    sleep 3
else
    log_message "${RED}ERROR: BattleServer directory not found${NC}"
fi

# Stop GateServer (FIXED PATH)
if [ -d "$SCRIPT_DIR/snk.zone.101/GateServer" ]; then
    log_message "Stopping GateServer..."
    cd "$SCRIPT_DIR/snk.zone.101/GateServer"
    ./stopall.sh
    sleep 3
else
    log_message "${RED}ERROR: GateServer directory not found${NC}"
fi

# Stop core services
log_message "Stopping core game services..."
for dir in */; do
    # Remove trailing /
    dir=${dir%/}

    # Skip directories containing dots (zone-specific directories)
    if [[ "$dir" == *.* ]]; then
        log_message "Skipping zone directory: $dir"
        continue
    fi

    # Check if stop.sh exists in directory
    if [ -f "$dir/stop.sh" ]; then
        log_message "Stopping service in directory: $dir"
        (
            cd "$dir" || exit
            ./stop.sh
            sleep 1
        )
    else
        log_message "No stop.sh found in directory: $dir"
    fi
done

# Return to original directory
cd "$SCRIPT_DIR"

log_message "${GREEN}=== Automatic Shutdown Complete ===${NC}"
log_message "All game server services have been stopped"
