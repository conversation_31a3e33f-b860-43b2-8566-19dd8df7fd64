#!/bin/bash

# SNK Game Server Manual Start Script
# Author: Auto-generated for SNK Game Management
# Description: Manually starts game servers with proper dependency checking and error handling

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="/data/applog/SNK"
STARTUP_LOG="/data/applog/startup_manual.log"
MAX_WAIT_TIME=30
SLEEP_BETWEEN_SERVICES=3

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$STARTUP_LOG"
}

# Error handling
set -e
trap 'log_message "${RED}ERROR: <PERSON><PERSON><PERSON> terminated unexpectedly at line $LINENO${NC}"' ERR

# Create log directories
mkdir -p "$LOG_DIR"
mkdir -p "/data/applog/ta_log"

log_message "${BLUE}=== SNK Game Server Manual Startup ===${NC}"
log_message "Starting manual game server startup sequence..."

# Function to check if a port is listening
check_port() {
    local port=$1
    local service_name=$2
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        log_message "${GREEN}✓ $service_name (port $port) is running${NC}"
        return 0
    else
        log_message "${RED}✗ $service_name (port $port) is not running${NC}"
        return 1
    fi
}

# Function to check service health
check_service() {
    local service=$1
    if systemctl is-active --quiet "$service" 2>/dev/null; then
        log_message "${GREEN}✓ $service is active${NC}"
        return 0
    else
        log_message "${RED}✗ $service is not active${NC}"
        return 1
    fi
}

# Function to wait for process to start
wait_for_process() {
    local process_name=$1
    local max_wait=$2
    local count=0
    
    while [ $count -lt $max_wait ]; do
        if pgrep -f "$process_name" > /dev/null; then
            return 0
        fi
        sleep 1
        ((count++))
    done
    return 1
}

# Check system dependencies
log_message "${YELLOW}=== Checking System Dependencies ===${NC}"

# Check MySQL
if ! check_service mysql; then
    log_message "${YELLOW}Starting MySQL service...${NC}"
    systemctl start mysql || {
        log_message "${RED}Failed to start MySQL service${NC}"
        exit 1
    }
    sleep 5
fi

# Check Redis
if ! check_service redis; then
    log_message "${YELLOW}Starting Redis service...${NC}"
    systemctl start redis || {
        log_message "${RED}Failed to start Redis service${NC}"
        exit 1
    }
    sleep 3
fi

# Check SFW Framework
log_message "${YELLOW}=== Checking SFW Framework ===${NC}"
sfw_services=("sfwregistry:2000" "sfwcontrol:2001" "sfwagent:2002")
sfw_failed=false

for service_port in "${sfw_services[@]}"; do
    service=$(echo "$service_port" | cut -d: -f1)
    port=$(echo "$service_port" | cut -d: -f2)
    
    if ! check_port "$port" "$service"; then
        log_message "${YELLOW}SFW Framework not fully running. Starting SFW services...${NC}"
        cd /data/sfw
        ./start.sh
        sleep 10
        sfw_failed=true
        break
    fi
done

if [ "$sfw_failed" = true ]; then
    # Verify SFW started successfully
    sleep 5
    for service_port in "${sfw_services[@]}"; do
        service=$(echo "$service_port" | cut -d: -f1)
        port=$(echo "$service_port" | cut -d: -f2)
        
        if ! check_port "$port" "$service"; then
            log_message "${RED}Failed to start SFW Framework service: $service${NC}"
            exit 1
        fi
    done
fi

# Clear old logs
log_message "${YELLOW}=== Clearing Old Logs ===${NC}"
rm -rf /data/applog/SNK/*

# Start game servers in proper order
log_message "${YELLOW}=== Starting Core Game Services ===${NC}"

# Define service startup order
core_services=(
    "AccountServer"
    "LoginServer" 
    "GlobalServer"
    "PlatformServer"
    "BgipProxy"
    "ForwardServer"
    "FriendServer"
    "MatchServer"
    "PlayServer"
    "RankServer"
    "RegionServer"
    "TeamServer"
    "IAPServer"
)

# Start core services
for service in "${core_services[@]}"; do
    if [ -d "$SCRIPT_DIR/$service" ] && [ -f "$SCRIPT_DIR/$service/start.sh" ]; then
        log_message "Starting $service..."
        cd "$SCRIPT_DIR/$service"
        ./start.sh
        sleep $SLEEP_BETWEEN_SERVICES
        
        # Verify service started
        if wait_for_process "$service" 10; then
            log_message "${GREEN}✓ $service started successfully${NC}"
        else
            log_message "${YELLOW}⚠ $service may not have started properly${NC}"
        fi
    else
        log_message "${YELLOW}⚠ $service directory or start script not found${NC}"
    fi
done

# Start zone-specific services
log_message "${YELLOW}=== Starting Zone Services ===${NC}"

# Start GateServer
if [ -d "$SCRIPT_DIR/snk.zone.101/GateServer" ]; then
    log_message "Starting GateServer..."
    cd "$SCRIPT_DIR/snk.zone.101/GateServer"
    ./start.sh 1
    sleep $SLEEP_BETWEEN_SERVICES
    
    if check_port 8101 "GateServer"; then
        log_message "${GREEN}✓ GateServer started successfully${NC}"
    else
        log_message "${YELLOW}⚠ GateServer may not have started properly${NC}"
    fi
fi

# Start BattleServer
if [ -d "$SCRIPT_DIR/snk.battle.101/BattleServer" ]; then
    log_message "Starting BattleServer..."
    cd "$SCRIPT_DIR/snk.battle.101/BattleServer"
    ./start.sh
    sleep $SLEEP_BETWEEN_SERVICES
    
    if check_port 8301 "BattleServer"; then
        log_message "${GREEN}✓ BattleServer started successfully${NC}"
    else
        log_message "${YELLOW}⚠ BattleServer may not have started properly${NC}"
    fi
fi

# Start GameServer (multiple instances)
if [ -d "$SCRIPT_DIR/snk.zone.101/GameServer" ]; then
    log_message "Starting GameServer instances..."
    cd "$SCRIPT_DIR/snk.zone.101/GameServer"
    ./startall.sh
    sleep 5
    
    if check_port 8201 "GameServer"; then
        log_message "${GREEN}✓ GameServer started successfully${NC}"
    else
        log_message "${YELLOW}⚠ GameServer may not have started properly${NC}"
    fi
fi

# Final status check
log_message "${YELLOW}=== Final System Status Check ===${NC}"

# Check critical ports
critical_ports=(
    "2000:SFW Registry"
    "2001:SFW Control" 
    "2002:SFW Agent"
    "3306:MySQL"
    "6379:Redis"
    "8101:GateServer"
    "8201:GameServer"
    "8301:BattleServer"
)

failed_services=0
for port_service in "${critical_ports[@]}"; do
    port=$(echo "$port_service" | cut -d: -f1)
    service=$(echo "$port_service" | cut -d: -f2)
    
    if check_port "$port" "$service"; then
        continue
    else
        ((failed_services++))
    fi
done

# Summary
log_message "${BLUE}=== Startup Summary ===${NC}"
if [ $failed_services -eq 0 ]; then
    log_message "${GREEN}✅ All critical services started successfully!${NC}"
    log_message "Game server system is ready for operation."
else
    log_message "${YELLOW}⚠ $failed_services critical services failed to start${NC}"
    log_message "Please check individual service logs in /data/applog/SNK/"
fi

# Show running processes
log_message "${BLUE}=== Running Game Processes ===${NC}"
ps aux | grep -E "(Server|server)" | grep -v grep | while read line; do
    log_message "$line"
done

log_message "${BLUE}=== Manual Startup Complete ===${NC}"
log_message "Use './stop_manual.sh' to stop all services"
log_message "Use '/data/game_status_dashboard.sh' to monitor system status"
