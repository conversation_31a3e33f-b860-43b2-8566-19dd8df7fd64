#!/bin/bash

# SNK Game Server Scripts Testing Suite
# Author: Auto-generated for SNK Game Management
# Description: Comprehensive testing suite for all server management scripts

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_LOG="/data/applog/script_testing.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$TEST_LOG"
}

# Test function
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    ((TESTS_TOTAL++))
    log_message "${BLUE}Running Test: $test_name${NC}"
    
    if eval "$test_command"; then
        if [ "$expected_result" = "pass" ]; then
            log_message "${GREEN}✓ PASS: $test_name${NC}"
            ((TESTS_PASSED++))
        else
            log_message "${RED}✗ FAIL: $test_name (expected failure but passed)${NC}"
            ((TESTS_FAILED++))
        fi
    else
        if [ "$expected_result" = "fail" ]; then
            log_message "${GREEN}✓ PASS: $test_name (expected failure)${NC}"
            ((TESTS_PASSED++))
        else
            log_message "${RED}✗ FAIL: $test_name${NC}"
            ((TESTS_FAILED++))
        fi
    fi
}

# Create test log directory
mkdir -p "/data/applog"

log_message "${BLUE}========================================${NC}"
log_message "${BLUE}    SNK Game Server Scripts Testing    ${NC}"
log_message "${BLUE}========================================${NC}"

# Test 1: Script Existence
log_message "${YELLOW}=== Test Suite 1: Script Existence ===${NC}"

run_test "SNK start_manual.sh exists" "[ -f '$SCRIPT_DIR/SNK/start_manual.sh' ]" "pass"
run_test "SNK stop_manual.sh exists" "[ -f '$SCRIPT_DIR/SNK/stop_manual.sh' ]" "pass"
run_test "SNK start.sh exists" "[ -f '$SCRIPT_DIR/SNK/start.sh' ]" "pass"
run_test "SNK stop.sh exists" "[ -f '$SCRIPT_DIR/SNK/stop.sh' ]" "pass"
run_test "SFW start_manual.sh exists" "[ -f '$SCRIPT_DIR/sfw/start_manual.sh' ]" "pass"
run_test "SFW stop_manual.sh exists" "[ -f '$SCRIPT_DIR/sfw/stop_manual.sh' ]" "pass"
run_test "system_start.sh exists" "[ -f '$SCRIPT_DIR/system_start.sh' ]" "pass"
run_test "system_stop.sh exists" "[ -f '$SCRIPT_DIR/system_stop.sh' ]" "pass"

# Test 2: Script Permissions
log_message "${YELLOW}=== Test Suite 2: Script Permissions ===${NC}"

run_test "SNK start_manual.sh is executable" "[ -x '$SCRIPT_DIR/SNK/start_manual.sh' ]" "pass"
run_test "SNK stop_manual.sh is executable" "[ -x '$SCRIPT_DIR/SNK/stop_manual.sh' ]" "pass"
run_test "SNK start.sh is executable" "[ -x '$SCRIPT_DIR/SNK/start.sh' ]" "pass"
run_test "SNK stop.sh is executable" "[ -x '$SCRIPT_DIR/SNK/stop.sh' ]" "pass"
run_test "SFW start_manual.sh is executable" "[ -x '$SCRIPT_DIR/sfw/start_manual.sh' ]" "pass"
run_test "SFW stop_manual.sh is executable" "[ -x '$SCRIPT_DIR/sfw/stop_manual.sh' ]" "pass"
run_test "system_start.sh is executable" "[ -x '$SCRIPT_DIR/system_start.sh' ]" "pass"
run_test "system_stop.sh is executable" "[ -x '$SCRIPT_DIR/system_stop.sh' ]" "pass"

# Test 3: Script Syntax
log_message "${YELLOW}=== Test Suite 3: Script Syntax ===${NC}"

run_test "SNK start_manual.sh syntax" "bash -n '$SCRIPT_DIR/SNK/start_manual.sh'" "pass"
run_test "SNK stop_manual.sh syntax" "bash -n '$SCRIPT_DIR/SNK/stop_manual.sh'" "pass"
run_test "SNK start.sh syntax" "bash -n '$SCRIPT_DIR/SNK/start.sh'" "pass"
run_test "SNK stop.sh syntax" "bash -n '$SCRIPT_DIR/SNK/stop.sh'" "pass"
run_test "SFW start_manual.sh syntax" "bash -n '$SCRIPT_DIR/sfw/start_manual.sh'" "pass"
run_test "SFW stop_manual.sh syntax" "bash -n '$SCRIPT_DIR/sfw/stop_manual.sh'" "pass"
run_test "system_start.sh syntax" "bash -n '$SCRIPT_DIR/system_start.sh'" "pass"
run_test "system_stop.sh syntax" "bash -n '$SCRIPT_DIR/system_stop.sh'" "pass"

# Test 4: Required Directories
log_message "${YELLOW}=== Test Suite 4: Required Directories ===${NC}"

run_test "SNK directory exists" "[ -d '$SCRIPT_DIR/SNK' ]" "pass"
run_test "SFW directory exists" "[ -d '$SCRIPT_DIR/sfw' ]" "pass"
run_test "Log directory exists" "[ -d '/data/applog' ] || mkdir -p '/data/applog'" "pass"

# Test 5: Script Content Validation
log_message "${YELLOW}=== Test Suite 5: Script Content Validation ===${NC}"

run_test "SNK start_manual.sh has shebang" "head -1 '$SCRIPT_DIR/SNK/start_manual.sh' | grep -q '#!/bin/bash'" "pass"
run_test "SNK stop_manual.sh has shebang" "head -1 '$SCRIPT_DIR/SNK/stop_manual.sh' | grep -q '#!/bin/bash'" "pass"
run_test "system_start.sh has error handling" "grep -q 'set -e' '$SCRIPT_DIR/system_start.sh'" "pass"
run_test "system_stop.sh has error handling" "grep -q 'trap' '$SCRIPT_DIR/system_stop.sh'" "pass"

# Test 6: Path Corrections
log_message "${YELLOW}=== Test Suite 6: Path Corrections ===${NC}"

run_test "SNK start.sh has correct paths" "! grep -q '/data/app/SNK/' '$SCRIPT_DIR/SNK/start.sh'" "pass"
run_test "SNK stop.sh has correct paths" "! grep -q '/data/app/SNK/' '$SCRIPT_DIR/SNK/stop.sh'" "pass"
run_test "SNK start.sh uses SCRIPT_DIR" "grep -q 'SCRIPT_DIR' '$SCRIPT_DIR/SNK/start.sh'" "pass"
run_test "SNK stop.sh uses SCRIPT_DIR" "grep -q 'SCRIPT_DIR' '$SCRIPT_DIR/SNK/stop.sh'" "pass"

# Test 7: Logging Features
log_message "${YELLOW}=== Test Suite 7: Logging Features ===${NC}"

run_test "SNK start_manual.sh has logging" "grep -q 'log_message' '$SCRIPT_DIR/SNK/start_manual.sh'" "pass"
run_test "SNK stop_manual.sh has logging" "grep -q 'log_message' '$SCRIPT_DIR/SNK/stop_manual.sh'" "pass"
run_test "system_start.sh has logging" "grep -q 'log_message' '$SCRIPT_DIR/system_start.sh'" "pass"
run_test "system_stop.sh has logging" "grep -q 'log_message' '$SCRIPT_DIR/system_stop.sh'" "pass"

# Test 8: Dependency Checking
log_message "${YELLOW}=== Test Suite 8: Dependency Checking ===${NC}"

run_test "SNK start_manual.sh checks MySQL" "grep -q 'mysql' '$SCRIPT_DIR/SNK/start_manual.sh'" "pass"
run_test "SNK start_manual.sh checks Redis" "grep -q 'redis' '$SCRIPT_DIR/SNK/start_manual.sh'" "pass"
run_test "SNK start_manual.sh checks SFW" "grep -q 'sfw' '$SCRIPT_DIR/SNK/start_manual.sh'" "pass"
run_test "system_start.sh checks dependencies" "grep -q 'check_service' '$SCRIPT_DIR/system_start.sh'" "pass"

# Test 9: Documentation
log_message "${YELLOW}=== Test Suite 9: Documentation ===${NC}"

run_test "Server management guide exists" "[ -f '$SCRIPT_DIR/SERVER_MANAGEMENT_GUIDE.md' ]" "pass"
run_test "Deployment instructions exist" "[ -f '$SCRIPT_DIR/DEPLOYMENT_INSTRUCTIONS.md' ]" "pass"
run_test "Guide has troubleshooting section" "grep -q 'Troubleshooting' '$SCRIPT_DIR/SERVER_MANAGEMENT_GUIDE.md'" "pass"
run_test "Guide has quick start section" "grep -q 'Quick Start' '$SCRIPT_DIR/SERVER_MANAGEMENT_GUIDE.md'" "pass"

# Test 10: Safety Features
log_message "${YELLOW}=== Test Suite 10: Safety Features ===${NC}"

run_test "Scripts have error trapping" "grep -q 'trap' '$SCRIPT_DIR/SNK/start_manual.sh'" "pass"
run_test "Scripts have timeout handling" "grep -q 'MAX_WAIT' '$SCRIPT_DIR/SNK/start_manual.sh'" "pass"
run_test "Stop scripts have force cleanup" "grep -q 'pkill' '$SCRIPT_DIR/SNK/stop_manual.sh'" "pass"
run_test "Scripts have status verification" "grep -q 'check_port' '$SCRIPT_DIR/SNK/start_manual.sh'" "pass"

# Test Results Summary
log_message "${BLUE}========================================${NC}"
log_message "${BLUE}           Test Results Summary          ${NC}"
log_message "${BLUE}========================================${NC}"

log_message "Total Tests Run: $TESTS_TOTAL"
log_message "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
log_message "Tests Failed: ${RED}$TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    log_message "${GREEN}✅ ALL TESTS PASSED! Scripts are ready for deployment.${NC}"
    exit 0
else
    log_message "${RED}❌ $TESTS_FAILED TESTS FAILED! Please review and fix issues.${NC}"
    exit 1
fi
