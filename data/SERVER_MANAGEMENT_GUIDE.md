# SNK Game Server Management Guide

## Overview

This guide provides comprehensive instructions for managing the SNK game server system using the standardized start/stop scripts created for proper server management.

## System Architecture

```
SNK Game Server System
├── System Dependencies
│   ├── MySQL Database (port 3306)
│   └── Redis Cache (port 6379)
├── SFW Framework (Service Framework)
│   ├── sfwregistry (port 2000) - Service Discovery
│   ├── sfwcontrol (port 2001) - Service Management
│   └── sfwagent (port 2002) - Service Monitoring
└── Game Server Stack
    ├── Core Services (AccountServer, LoginServer, etc.)
    └── Zone Services (GateServer, GameServer, BattleServer)
```

## Available Scripts

### System-Wide Management
- **`/data/system_start.sh`** - Complete system startup (recommended)
- **`/data/system_stop.sh`** - Complete system shutdown (recommended)

### Game Server Management
- **`/data/SNK/start_manual.sh`** - Manual game server startup with full checks
- **`/data/SNK/stop_manual.sh`** - Manual game server shutdown with cleanup
- **`/data/SNK/start.sh`** - Automatic game server startup (fixed paths)
- **`/data/SNK/stop.sh`** - Automatic game server shutdown (fixed paths)

### SFW Framework Management
- **`/data/sfw/start_manual.sh`** - Manual SFW framework startup
- **`/data/sfw/stop_manual.sh`** - Manual SFW framework shutdown
- **`/data/sfw/start.sh`** - Automatic SFW framework startup
- **`/data/sfw/stop.sh`** - Automatic SFW framework shutdown

### Monitoring and Status
- **`/data/game_status_dashboard.sh`** - Real-time system status dashboard
- **`/data/auto_restart_monitor.sh`** - Auto-restart monitoring system

## Quick Start Guide

### Starting the Complete System
```bash
# Recommended: Complete system startup
sudo /data/system_start.sh

# Alternative: Step-by-step startup
sudo systemctl start mysql redis
cd /data/sfw && sudo ./start_manual.sh
cd /data/SNK && sudo ./start_manual.sh
```

### Stopping the Complete System
```bash
# Recommended: Complete system shutdown
sudo /data/system_stop.sh

# Alternative: Step-by-step shutdown
cd /data/SNK && sudo ./stop_manual.sh
cd /data/sfw && sudo ./stop_manual.sh
# Note: MySQL and Redis are left running by default
```

### Checking System Status
```bash
# Real-time dashboard
/data/game_status_dashboard.sh

# Check specific services
systemctl status mysql redis auto_restart
netstat -tlnp | grep -E "(2000|2001|2002|6379|8101|8201|8301)"
ps aux | grep -E "(Server|server)" | grep -v grep
```

## Detailed Usage Instructions

### 1. System-Wide Scripts

#### `/data/system_start.sh`
- **Purpose**: Complete system startup with dependency checking
- **Features**:
  - Starts MySQL and Redis if not running
  - Starts SFW Framework in proper order
  - Starts all game servers with verification
  - Enables auto-restart monitor if available
  - Comprehensive status reporting

#### `/data/system_stop.sh`
- **Purpose**: Complete system shutdown in proper order
- **Features**:
  - Stops auto-restart monitor first
  - Gracefully shuts down game servers
  - Stops SFW Framework
  - Leaves MySQL/Redis running (configurable)
  - Final verification and cleanup

### 2. Game Server Scripts

#### `/data/SNK/start_manual.sh`
- **Purpose**: Manual game server startup with full dependency checking
- **Features**:
  - Verifies MySQL, Redis, and SFW Framework are running
  - Starts services in proper dependency order
  - Waits for each service to fully start
  - Comprehensive error handling and logging
  - Final status verification

#### `/data/SNK/stop_manual.sh`
- **Purpose**: Manual game server shutdown with proper cleanup
- **Features**:
  - Stops services in reverse dependency order
  - Graceful shutdown with timeout handling
  - Force cleanup of remaining processes
  - Port status verification
  - Comprehensive logging

### 3. SFW Framework Scripts

#### `/data/sfw/start_manual.sh`
- **Purpose**: Manual SFW Framework startup
- **Features**:
  - Starts services in correct order (registry → control → agent)
  - Waits for each service to be ready
  - Port verification for each service
  - MySQL dependency checking

#### `/data/sfw/stop_manual.sh`
- **Purpose**: Manual SFW Framework shutdown
- **Features**:
  - Stops services in reverse order (agent → control → registry)
  - Graceful shutdown with force cleanup if needed
  - Port status verification

## Troubleshooting

### Common Issues and Solutions

#### 1. Services Won't Start
```bash
# Check dependencies
systemctl status mysql redis
netstat -tlnp | grep -E "(3306|6379)"

# Check SFW Framework
netstat -tlnp | grep -E "(2000|2001|2002)"

# Check logs
tail -f /data/applog/system_startup.log
tail -f /data/applog/SNK/*
```

#### 2. Services Won't Stop
```bash
# Force stop all game processes
pkill -f "Server|GameServer"

# Force stop SFW processes
pkill -f "sfw"

# Check remaining processes
ps aux | grep -E "(Server|sfw)" | grep -v grep
```

#### 3. Port Conflicts
```bash
# Check what's using a port
netstat -tlnp | grep :8101
lsof -i :8101

# Kill process using port
fuser -k 8101/tcp
```

#### 4. Permission Issues
```bash
# Fix script permissions
chmod +x /data/system_*.sh
chmod +x /data/SNK/*.sh
chmod +x /data/sfw/*.sh

# Fix log directory permissions
mkdir -p /data/applog/{SNK,sfw,ta_log}
chown -R root:root /data/applog
```

## Log Files and Monitoring

### Log Locations
- **System Logs**: `/data/applog/system_startup.log`, `/data/applog/system_shutdown.log`
- **Game Server Logs**: `/data/applog/SNK/*/`
- **SFW Framework Logs**: `/data/applog/sfw/*/`
- **Manual Script Logs**: `/data/applog/startup_manual.log`, `/data/applog/shutdown_manual.log`
- **Auto-Restart Logs**: `/data/applog/auto_restart/auto_restart.log`

### Monitoring Commands
```bash
# Real-time system status
/data/game_status_dashboard.sh

# Monitor startup logs
tail -f /data/applog/system_startup.log

# Monitor game server logs
tail -f /data/applog/SNK/*/SNK.*.log

# Monitor auto-restart system
tail -f /data/applog/auto_restart/auto_restart.log
```

## Best Practices

### 1. Startup Sequence
1. Always use system-wide scripts for complete operations
2. Verify dependencies before starting game servers
3. Allow sufficient time between service starts
4. Monitor logs during startup for errors

### 2. Shutdown Sequence
1. Stop auto-restart monitor first
2. Stop game servers before SFW Framework
3. Use graceful shutdown with force cleanup as backup
4. Verify all processes are stopped

### 3. Maintenance
1. Regularly check log files for errors
2. Monitor system resources during operation
3. Use the auto-restart monitor for production
4. Keep backups of configuration files

## Emergency Procedures

### Complete System Reset
```bash
# Emergency stop all services
sudo /data/system_stop.sh
pkill -f "Server|sfw"

# Clear logs and restart
rm -rf /data/applog/SNK/* /data/applog/sfw/*
sudo /data/system_start.sh
```

### Service Recovery
```bash
# Restart specific service
cd /data/SNK/ServiceName && ./stop.sh && ./start.sh

# Restart SFW Framework only
cd /data/sfw && ./stop_manual.sh && ./start_manual.sh

# Restart game servers only
cd /data/SNK && ./stop_manual.sh && ./start_manual.sh
```

## Support and Maintenance

For additional support:
1. Check log files in `/data/applog/`
2. Use the status dashboard: `/data/game_status_dashboard.sh`
3. Review this guide for troubleshooting steps
4. Contact system administrator if issues persist
