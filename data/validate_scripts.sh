#!/bin/bash

# Local Script Validation Tool
# Author: Auto-generated for SNK Game Management
# Description: Validates all scripts locally before deployment

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0

# Logging function
log_message() {
    echo -e "$1"
}

# Test function
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    log_message "${BLUE}Testing: $test_name${NC}"
    
    if eval "$test_command" >/dev/null 2>&1; then
        log_message "${GREEN}✓ PASS: $test_name${NC}"
        ((TESTS_PASSED++))
    else
        log_message "${RED}✗ FAIL: $test_name${NC}"
        ((TESTS_FAILED++))
    fi
}

log_message "${BLUE}========================================${NC}"
log_message "${BLUE}      Local Script Validation Tool     ${NC}"
log_message "${BLUE}========================================${NC}"

# Test 1: File Existence
log_message "${YELLOW}=== Testing File Existence ===${NC}"
run_test "SNK start_manual.sh exists" "[ -f '$SCRIPT_DIR/SNK/start_manual.sh' ]"
run_test "SNK stop_manual.sh exists" "[ -f '$SCRIPT_DIR/SNK/stop_manual.sh' ]"
run_test "SNK start.sh exists" "[ -f '$SCRIPT_DIR/SNK/start.sh' ]"
run_test "SNK stop.sh exists" "[ -f '$SCRIPT_DIR/SNK/stop.sh' ]"
run_test "SFW start_manual.sh exists" "[ -f '$SCRIPT_DIR/sfw/start_manual.sh' ]"
run_test "SFW stop_manual.sh exists" "[ -f '$SCRIPT_DIR/sfw/stop_manual.sh' ]"
run_test "system_start.sh exists" "[ -f '$SCRIPT_DIR/system_start.sh' ]"
run_test "system_stop.sh exists" "[ -f '$SCRIPT_DIR/system_stop.sh' ]"

# Test 2: Script Syntax
log_message "${YELLOW}=== Testing Script Syntax ===${NC}"
run_test "SNK start_manual.sh syntax" "bash -n '$SCRIPT_DIR/SNK/start_manual.sh'"
run_test "SNK stop_manual.sh syntax" "bash -n '$SCRIPT_DIR/SNK/stop_manual.sh'"
run_test "SNK start.sh syntax" "bash -n '$SCRIPT_DIR/SNK/start.sh'"
run_test "SNK stop.sh syntax" "bash -n '$SCRIPT_DIR/SNK/stop.sh'"
run_test "SFW start_manual.sh syntax" "bash -n '$SCRIPT_DIR/sfw/start_manual.sh'"
run_test "SFW stop_manual.sh syntax" "bash -n '$SCRIPT_DIR/sfw/stop_manual.sh'"
run_test "system_start.sh syntax" "bash -n '$SCRIPT_DIR/system_start.sh'"
run_test "system_stop.sh syntax" "bash -n '$SCRIPT_DIR/system_stop.sh'"

# Test 3: Required Content
log_message "${YELLOW}=== Testing Required Content ===${NC}"
run_test "SNK start_manual.sh has shebang" "head -1 '$SCRIPT_DIR/SNK/start_manual.sh' | grep -q '#!/bin/bash'"
run_test "SNK start_manual.sh has logging" "grep -q 'log_message' '$SCRIPT_DIR/SNK/start_manual.sh'"
run_test "SNK start_manual.sh checks dependencies" "grep -q 'mysql\|redis\|sfw' '$SCRIPT_DIR/SNK/start_manual.sh'"
run_test "SNK stop_manual.sh has cleanup" "grep -q 'pkill\|force' '$SCRIPT_DIR/SNK/stop_manual.sh'"
run_test "System start has error handling" "grep -q 'set -e\|trap' '$SCRIPT_DIR/system_start.sh'"
run_test "System stop has error handling" "grep -q 'trap' '$SCRIPT_DIR/system_stop.sh'"

# Test 4: Path Corrections
log_message "${YELLOW}=== Testing Path Corrections ===${NC}"
run_test "SNK start.sh no old paths" "! grep -q '/data/app/SNK/' '$SCRIPT_DIR/SNK/start.sh'"
run_test "SNK stop.sh no old paths" "! grep -q '/data/app/SNK/' '$SCRIPT_DIR/SNK/stop.sh'"
run_test "SNK start.sh uses SCRIPT_DIR" "grep -q 'SCRIPT_DIR' '$SCRIPT_DIR/SNK/start.sh'"
run_test "SNK stop.sh uses SCRIPT_DIR" "grep -q 'SCRIPT_DIR' '$SCRIPT_DIR/SNK/stop.sh'"

# Test 5: Documentation
log_message "${YELLOW}=== Testing Documentation ===${NC}"
run_test "Server management guide exists" "[ -f '$SCRIPT_DIR/SERVER_MANAGEMENT_GUIDE.md' ]"
run_test "Deployment instructions exist" "[ -f '$SCRIPT_DIR/DEPLOYMENT_INSTRUCTIONS.md' ]"
run_test "Guide has troubleshooting" "grep -q -i 'troubleshoot' '$SCRIPT_DIR/SERVER_MANAGEMENT_GUIDE.md'"

# Test 6: Deployment Tools
log_message "${YELLOW}=== Testing Deployment Tools ===${NC}"
run_test "Deployment script exists" "[ -f '$SCRIPT_DIR/deploy_to_server.sh' ]"
run_test "Validation script exists" "[ -f '$SCRIPT_DIR/validate_scripts.sh' ]"

# Summary
log_message "${BLUE}========================================${NC}"
log_message "${BLUE}         Validation Summary             ${NC}"
log_message "${BLUE}========================================${NC}"

TOTAL_TESTS=$((TESTS_PASSED + TESTS_FAILED))
log_message "Total Tests: $TOTAL_TESTS"
log_message "Passed: ${GREEN}$TESTS_PASSED${NC}"
log_message "Failed: ${RED}$TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    log_message "${GREEN}✅ ALL VALIDATIONS PASSED!${NC}"
    log_message "${GREEN}Scripts are ready for deployment to server.${NC}"
    
    log_message "${BLUE}Next Steps:${NC}"
    log_message "1. Run deployment: ./data/deploy_to_server.sh"
    log_message "2. Test on server after deployment"
    log_message "3. Monitor system status"
    
    exit 0
else
    log_message "${RED}❌ $TESTS_FAILED VALIDATIONS FAILED!${NC}"
    log_message "${RED}Please fix issues before deployment.${NC}"
    exit 1
fi
