#!/bin/bash

# SNK Game System Complete Startup Script
# Author: Auto-generated for SNK Game Management
# Description: Starts the complete SNK game system including all dependencies

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/data/applog/system_startup.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
set -e
trap 'log_message "${RED}ERROR: System startup failed at line $LINENO${NC}"; exit 1' ERR

# Create log directories
mkdir -p "/data/applog"
mkdir -p "/data/applog/SNK"
mkdir -p "/data/applog/sfw"
mkdir -p "/data/applog/ta_log"

log_message "${BLUE}========================================${NC}"
log_message "${BLUE}    SNK Game System Complete Startup   ${NC}"
log_message "${BLUE}========================================${NC}"

# Function to check service status
check_service() {
    local service=$1
    if systemctl is-active --quiet "$service" 2>/dev/null; then
        log_message "${GREEN}✓ $service is active${NC}"
        return 0
    else
        log_message "${RED}✗ $service is not active${NC}"
        return 1
    fi
}

# Function to check port
check_port() {
    local port=$1
    local service_name=$2
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        log_message "${GREEN}✓ $service_name (port $port) is running${NC}"
        return 0
    else
        log_message "${RED}✗ $service_name (port $port) is not running${NC}"
        return 1
    fi
}

# Step 1: Start System Dependencies
log_message "${YELLOW}=== Step 1: Starting System Dependencies ===${NC}"

# Start MySQL
log_message "Checking MySQL database service..."
if ! check_service mysql; then
    log_message "Starting MySQL service..."
    systemctl start mysql
    sleep 5
    if check_service mysql; then
        log_message "${GREEN}✓ MySQL started successfully${NC}"
    else
        log_message "${RED}✗ Failed to start MySQL${NC}"
        exit 1
    fi
fi

# Start Redis
log_message "Checking Redis cache service..."
if ! check_service redis; then
    log_message "Starting Redis service..."
    systemctl start redis
    sleep 3
    if check_service redis; then
        log_message "${GREEN}✓ Redis started successfully${NC}"
    else
        log_message "${RED}✗ Failed to start Redis${NC}"
        exit 1
    fi
fi

# Step 2: Start SFW Framework
log_message "${YELLOW}=== Step 2: Starting SFW Framework ===${NC}"

# Check if SFW is already running
sfw_running=true
if ! check_port 2000 "sfwregistry"; then sfw_running=false; fi
if ! check_port 2001 "sfwcontrol"; then sfw_running=false; fi
if ! check_port 2002 "sfwagent"; then sfw_running=false; fi

if [ "$sfw_running" = false ]; then
    log_message "Starting SFW Framework..."
    cd "$SCRIPT_DIR/sfw"
    if [ -f "start_manual.sh" ]; then
        ./start_manual.sh
    else
        ./start.sh
        sleep 10
    fi
    
    # Verify SFW started
    if check_port 2000 "sfwregistry" && check_port 2001 "sfwcontrol" && check_port 2002 "sfwagent"; then
        log_message "${GREEN}✓ SFW Framework started successfully${NC}"
    else
        log_message "${RED}✗ SFW Framework failed to start completely${NC}"
        exit 1
    fi
else
    log_message "${GREEN}✓ SFW Framework is already running${NC}"
fi

# Step 3: Start Game Servers
log_message "${YELLOW}=== Step 3: Starting Game Servers ===${NC}"

cd "$SCRIPT_DIR/SNK"
if [ -f "start_manual.sh" ]; then
    log_message "Using manual game server startup..."
    ./start_manual.sh
else
    log_message "Using automatic game server startup..."
    ./start.sh
fi

# Step 4: Final System Verification
log_message "${YELLOW}=== Step 4: Final System Verification ===${NC}"

# Check critical services
critical_services=(
    "3306:MySQL Database"
    "6379:Redis Cache"
    "2000:SFW Registry"
    "2001:SFW Control"
    "2002:SFW Agent"
    "8101:GateServer"
    "8201:GameServer"
    "8301:BattleServer"
)

failed_services=0
for service_port in "${critical_services[@]}"; do
    port=$(echo "$service_port" | cut -d: -f1)
    service=$(echo "$service_port" | cut -d: -f2)
    
    if ! check_port "$port" "$service"; then
        ((failed_services++))
    fi
done

# Step 5: Start Auto-Restart Monitor (Optional)
log_message "${YELLOW}=== Step 5: Auto-Restart Monitor ===${NC}"

if systemctl is-enabled auto_restart >/dev/null 2>&1; then
    if ! check_service auto_restart; then
        log_message "Starting auto-restart monitor..."
        systemctl start auto_restart
        sleep 3
        if check_service auto_restart; then
            log_message "${GREEN}✓ Auto-restart monitor started${NC}"
        else
            log_message "${YELLOW}⚠ Auto-restart monitor failed to start${NC}"
        fi
    else
        log_message "${GREEN}✓ Auto-restart monitor is already running${NC}"
    fi
else
    log_message "${YELLOW}⚠ Auto-restart monitor service not installed${NC}"
fi

# Final Summary
log_message "${BLUE}========================================${NC}"
log_message "${BLUE}        System Startup Summary          ${NC}"
log_message "${BLUE}========================================${NC}"

if [ $failed_services -eq 0 ]; then
    log_message "${GREEN}✅ COMPLETE SUCCESS: All services started successfully!${NC}"
    log_message "${GREEN}   SNK Game System is fully operational${NC}"
    
    # Show system status
    log_message "${BLUE}=== System Status ===${NC}"
    log_message "Game Server Processes: $(ps aux | grep -E '(Server|server)' | grep -v grep | wc -l)"
    log_message "SFW Framework Processes: $(ps aux | grep -E '(sfw|SFW)' | grep -v grep | wc -l)"
    
else
    log_message "${YELLOW}⚠ PARTIAL SUCCESS: $failed_services critical services failed${NC}"
    log_message "${YELLOW}   System may not be fully operational${NC}"
fi

log_message "${BLUE}=== Quick Commands ===${NC}"
log_message "System Status:     /data/game_status_dashboard.sh"
log_message "Stop System:       /data/system_stop.sh"
log_message "View Logs:         tail -f /data/applog/system_startup.log"
log_message "Game Server Logs:  tail -f /data/applog/SNK/*"

log_message "${BLUE}=== System Startup Complete ===${NC}"
