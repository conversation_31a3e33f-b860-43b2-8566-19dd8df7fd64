# Server Deployment Instructions

## Overview
This document provides step-by-step instructions for deploying the standardized start/stop scripts to the game server at IP **************.

## Prerequisites
- SSH access to server: `ssh root@**************`
- Server should have existing `/data/SNK` and `/data/sfw` directories
- MySQL and Redis should be installed and configured

## Deployment Steps

### Step 1: Connect to Server
```bash
ssh root@**************
```

### Step 2: Backup Existing Scripts
```bash
# Create backup directory
mkdir -p /data/backup/$(date +%Y%m%d_%H%M%S)
cd /data/backup/$(date +%Y%m%d_%H%M%S)

# Backup existing scripts
cp /data/SNK/start.sh ./start_snk_old.sh 2>/dev/null || echo "No existing SNK start.sh"
cp /data/SNK/stop.sh ./stop_snk_old.sh 2>/dev/null || echo "No existing SNK stop.sh"
cp /data/sfw/start.sh ./start_sfw_old.sh 2>/dev/null || echo "No existing SFW start.sh"
cp /data/sfw/stop.sh ./stop_sfw_old.sh 2>/dev/null || echo "No existing SFW stop.sh"

echo "Backup completed in $(pwd)"
```

### Step 3: Create Required Directories
```bash
# Create log directories
mkdir -p /data/applog/{SNK,sfw,ta_log,auto_restart}

# Set proper permissions
chown -R root:root /data/applog
chmod -R 755 /data/applog
```

### Step 4: Deploy SNK Game Server Scripts

#### Deploy start_manual.sh
```bash
cat > /data/SNK/start_manual.sh << 'EOF'
[Copy the entire content of data/SNK/start_manual.sh from the local codebase]
EOF

chmod +x /data/SNK/start_manual.sh
```

#### Deploy stop_manual.sh
```bash
cat > /data/SNK/stop_manual.sh << 'EOF'
[Copy the entire content of data/SNK/stop_manual.sh from the local codebase]
EOF

chmod +x /data/SNK/stop_manual.sh
```

#### Update existing start.sh
```bash
cat > /data/SNK/start.sh << 'EOF'
[Copy the entire content of data/SNK/start.sh from the local codebase]
EOF

chmod +x /data/SNK/start.sh
```

#### Update existing stop.sh
```bash
cat > /data/SNK/stop.sh << 'EOF'
[Copy the entire content of data/SNK/stop.sh from the local codebase]
EOF

chmod +x /data/SNK/stop.sh
```

### Step 5: Deploy SFW Framework Scripts

#### Deploy SFW start_manual.sh
```bash
cat > /data/sfw/start_manual.sh << 'EOF'
[Copy the entire content of data/sfw/start_manual.sh from the local codebase]
EOF

chmod +x /data/sfw/start_manual.sh
```

#### Deploy SFW stop_manual.sh
```bash
cat > /data/sfw/stop_manual.sh << 'EOF'
[Copy the entire content of data/sfw/stop_manual.sh from the local codebase]
EOF

chmod +x /data/sfw/stop_manual.sh
```

### Step 6: Deploy System-Wide Scripts

#### Deploy system_start.sh
```bash
cat > /data/system_start.sh << 'EOF'
[Copy the entire content of data/system_start.sh from the local codebase]
EOF

chmod +x /data/system_start.sh
```

#### Deploy system_stop.sh
```bash
cat > /data/system_stop.sh << 'EOF'
[Copy the entire content of data/system_stop.sh from the local codebase]
EOF

chmod +x /data/system_stop.sh
```

### Step 7: Deploy Documentation
```bash
cat > /data/SERVER_MANAGEMENT_GUIDE.md << 'EOF'
[Copy the entire content of data/SERVER_MANAGEMENT_GUIDE.md from the local codebase]
EOF
```

### Step 8: Verify Deployment
```bash
# Check script permissions
ls -la /data/SNK/*.sh
ls -la /data/sfw/*.sh
ls -la /data/system_*.sh

# Verify script syntax
bash -n /data/SNK/start_manual.sh
bash -n /data/SNK/stop_manual.sh
bash -n /data/sfw/start_manual.sh
bash -n /data/sfw/stop_manual.sh
bash -n /data/system_start.sh
bash -n /data/system_stop.sh

echo "All scripts deployed and syntax verified!"
```

## Testing Procedures

### Test 1: SFW Framework Management
```bash
# Test SFW manual scripts
cd /data/sfw
./stop_manual.sh
sleep 5
./start_manual.sh

# Verify SFW services
netstat -tlnp | grep -E "(2000|2001|2002)"
```

### Test 2: Game Server Management
```bash
# Test game server manual scripts
cd /data/SNK
./stop_manual.sh
sleep 10
./start_manual.sh

# Verify game services
netstat -tlnp | grep -E "(8101|8201|8301)"
ps aux | grep -E "(Server|server)" | grep -v grep
```

### Test 3: System-Wide Management
```bash
# Test complete system management
/data/system_stop.sh
sleep 15
/data/system_start.sh

# Check system status
/data/game_status_dashboard.sh
```

### Test 4: Log Verification
```bash
# Check log files are created
ls -la /data/applog/
tail -20 /data/applog/system_startup.log
tail -20 /data/applog/startup_manual.log
```

## Rollback Procedures

If issues occur, rollback to previous scripts:

```bash
# Find backup directory
ls -la /data/backup/

# Restore from most recent backup
BACKUP_DIR=$(ls -1t /data/backup/ | head -1)
cd /data/backup/$BACKUP_DIR

# Restore old scripts
cp start_snk_old.sh /data/SNK/start.sh 2>/dev/null || echo "No SNK start.sh backup"
cp stop_snk_old.sh /data/SNK/stop.sh 2>/dev/null || echo "No SNK stop.sh backup"
cp start_sfw_old.sh /data/sfw/start.sh 2>/dev/null || echo "No SFW start.sh backup"
cp stop_sfw_old.sh /data/sfw/stop.sh 2>/dev/null || echo "No SFW stop.sh backup"

# Remove new scripts
rm -f /data/SNK/start_manual.sh /data/SNK/stop_manual.sh
rm -f /data/sfw/start_manual.sh /data/sfw/stop_manual.sh
rm -f /data/system_start.sh /data/system_stop.sh

echo "Rollback completed"
```

## Post-Deployment Configuration

### Enable Auto-Restart Monitor (Optional)
```bash
# If auto-restart service exists
systemctl enable auto_restart
systemctl start auto_restart
systemctl status auto_restart
```

### Create Cron Jobs (Optional)
```bash
# Add to root crontab for daily health checks
crontab -e

# Add these lines:
# Daily system health check at 2 AM
0 2 * * * /data/game_status_dashboard.sh > /data/applog/daily_health_check.log 2>&1

# Weekly log cleanup at 3 AM on Sundays
0 3 * * 0 find /data/applog -name "*.log" -mtime +7 -delete
```

## Monitoring and Maintenance

### Daily Checks
```bash
# Check system status
/data/game_status_dashboard.sh

# Check log files for errors
grep -i error /data/applog/system_*.log
grep -i error /data/applog/SNK/*/SNK.*.log
```

### Weekly Maintenance
```bash
# Clean old log files
find /data/applog -name "*.log" -mtime +7 -delete

# Check disk space
df -h /data

# Verify all services are running
netstat -tlnp | grep -E "(2000|2001|2002|6379|8101|8201|8301)"
```

## Troubleshooting Common Issues

### Issue 1: Permission Denied
```bash
chmod +x /data/SNK/*.sh /data/sfw/*.sh /data/system_*.sh
chown root:root /data/SNK/*.sh /data/sfw/*.sh /data/system_*.sh
```

### Issue 2: Directory Not Found
```bash
mkdir -p /data/applog/{SNK,sfw,ta_log,auto_restart}
```

### Issue 3: Service Won't Start
```bash
# Check dependencies
systemctl status mysql redis
netstat -tlnp | grep -E "(3306|6379)"

# Check logs
tail -50 /data/applog/system_startup.log
```

## Support Contacts

- System Administrator: [Contact Information]
- Emergency Contact: [Emergency Contact]
- Documentation: `/data/SERVER_MANAGEMENT_GUIDE.md`

## Deployment Checklist

- [ ] Server access confirmed
- [ ] Existing scripts backed up
- [ ] Required directories created
- [ ] SNK scripts deployed and tested
- [ ] SFW scripts deployed and tested
- [ ] System-wide scripts deployed and tested
- [ ] Documentation deployed
- [ ] All tests passed
- [ ] Monitoring configured
- [ ] Team notified of deployment
