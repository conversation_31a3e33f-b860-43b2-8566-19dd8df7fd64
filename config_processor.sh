#!/bin/bash

# SNK Game Server Configuration Template Processor
# This script processes template configuration files and replaces placeholders with actual values

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Configuration variables - CONFIGURED FOR AAPANEL SERVER
NEW_SERVER_IP="${NEW_SERVER_IP:-**************}"
REDIS_PASSWORD="${REDIS_PASSWORD:-redis_snk_2024}"
DB_PASSWORD="${DB_PASSWORD:-snk_game_2024}"
DB_USER="${DB_USER:-snk_user}"
GAME_ID="${GAME_ID:-101}"
ZONE_ID="${ZONE_ID:-101}"
GATE_ID="${GATE_ID:-1}"
GAME_NUM="${GAME_NUM:-1}"
GATE_NUM="${GATE_NUM:-1}"

# Derived variables
ZONE_DIVISION="snk.zone.${ZONE_ID}"
BATTLE_DIVISION="snk.battle.${ZONE_ID}"

log_message "${BLUE}========================================${NC}"
log_message "${BLUE}  SNK Configuration Template Processor ${NC}"
log_message "${BLUE}========================================${NC}"

# Validate required variables
if [ "$NEW_SERVER_IP" = "127.0.0.1" ]; then
    log_message "${RED}ERROR: NEW_SERVER_IP must be set to the actual server IP${NC}"
    exit 1
fi

if [ "$REDIS_PASSWORD" = "your_redis_password" ]; then
    log_message "${YELLOW}WARNING: Using default Redis password. Please change REDIS_PASSWORD${NC}"
fi

if [ "$DB_PASSWORD" = "your_db_password" ]; then
    log_message "${YELLOW}WARNING: Using default database password. Please change DB_PASSWORD${NC}"
fi

log_message "${BLUE}Configuration Parameters:${NC}"
log_message "Server IP: $NEW_SERVER_IP"
log_message "Database User: $DB_USER"
log_message "Game ID: $GAME_ID"
log_message "Zone ID: $ZONE_ID"
log_message "Gate ID: $GATE_ID"
log_message "Zone Division: $ZONE_DIVISION"

# Function to process template files
process_template() {
    local template_file="$1"
    local output_file="$2"
    
    log_message "Processing: $template_file -> $output_file"
    
    # Create output directory if it doesn't exist
    mkdir -p "$(dirname "$output_file")"
    
    # Process the template file with CORRECTED IP configurations
    sed -e "s/TEMPLATE_REGISTRY_ENDPOINT/tcp -h 127.0.0.1 -p 2000/g" \
        -e "s/TEMPLATE_ServerIP/127.0.0.1/g" \
        -e "s/TEMPLATE_RegistryDbHost/$NEW_SERVER_IP/g" \
        -e "s/TEMPLATE_RegistryDbUser/$DB_USER/g" \
        -e "s/TEMPLATE_RegistryDbPasswd/$DB_PASSWORD/g" \
        -e "s/TEMPLATE_GameDB/$NEW_SERVER_IP/g" \
        -e "s/TEMPLATE_GAME_DBNAME/db_zone_$ZONE_ID/g" \
        -e "s/TEMPLATE_GAME_DBPORT/3306/g" \
        -e "s/TEMPLATE_DB_USER/$DB_USER/g" \
        -e "s/TEMPLATE_DB_PASSWD/$DB_PASSWORD/g" \
        -e "s/TEMPLATE_Redis_Host/$NEW_SERVER_IP/g" \
        -e "s/TEMPLATE_Redis_Port/6379/g" \
        -e "s/TEMPLATE_Redis_Password/$REDIS_PASSWORD/g" \
        -e "s/TEMPLATE_GAMEID/$GAME_ID/g" \
        -e "s/TEMPLATE_GAMENUM/$GAME_NUM/g" \
        -e "s/TEMPLATE_GATENUM/$GATE_NUM/g" \
        -e "s/TEMPLATE_GATEID/$GATE_ID/g" \
        -e "s/TEMPLATE_SET_DIVISION/$ZONE_DIVISION/g" \
        -e "s/TEMPLATE_ENDPOINT_HandleConn/tcp -h 0.0.0.0 -p 18101/g" \
        -e "s/TEMPLATE_ENDPOINT_GateServiceObj/tcp -h 127.0.0.1 -p 8101/g" \
        -e "s/TEMPLATE_ENDPOINT_GameServiceObj/tcp -h 127.0.0.1 -p 8201/g" \
        -e "s/TEMPLATE_ENDPOINT_LoginServiceObj/tcp -h 127.0.0.1 -p 7001/g" \
        -e "s/TEMPLATE_ENDPOINT_AccountServiceObj/tcp -h 127.0.0.1 -p 7011/g" \
        -e "s/TEMPLATE_ENDPOINT_GVGServiceObj/tcp -h 127.0.0.1 -p 7081/g" \
        -e "s/TEMPLATE_ENDPOINT_MatchServiceObj/tcp -h 127.0.0.1 -p 7051/g" \
        -e "s/TEMPLATE_ENDPOINT_GlobalServiceObj/tcp -h 127.0.0.1 -p 7031/g" \
        -e "s/TEMPLATE_ENDPOINT_TeamServiceObj/tcp -h 127.0.0.1 -p 7061/g" \
        -e "s/TEMPLATE_ENDPOINT_PlayServiceObj/tcp -h 127.0.0.1 -p 7071/g" \
        -e "s/TEMPLATE_IAPDB_HOST/$NEW_SERVER_IP/g" \
        -e "s/TEMPLATE_IAPDB_USER/$DB_USER/g" \
        -e "s/TEMPLATE_IAPDB_PASSWD/$DB_PASSWORD/g" \
        "$template_file" > "$output_file"
    
    # Verify no templates remain
    if grep -q "TEMPLATE_" "$output_file"; then
        log_message "${YELLOW}WARNING: Unprocessed templates found in $output_file:${NC}"
        grep "TEMPLATE_" "$output_file" | head -5
    fi
}

# Function to process all templates in a directory
process_directory() {
    local template_dir="$1"
    local output_base_dir="$2"
    
    if [ ! -d "$template_dir" ]; then
        log_message "${RED}ERROR: Template directory not found: $template_dir${NC}"
        return 1
    fi
    
    log_message "${YELLOW}Processing templates in: $template_dir${NC}"
    
    find "$template_dir" -type f \( -name "*.conf" -o -name "*.sfw.conf" \) | while read template; do
        # Calculate relative path
        relative_path=${template#$template_dir/}
        output_path="$output_base_dir/$relative_path"
        
        process_template "$template" "$output_path"
    done
}

# Main processing
log_message "${YELLOW}=== Processing Server Configuration Templates ===${NC}"

# Process ConfTemplate directory
if [ -d "server/ConfTemplate" ]; then
    process_directory "server/ConfTemplate" "server"
else
    log_message "${RED}ERROR: server/ConfTemplate directory not found${NC}"
    exit 1
fi

# Process SFW configuration templates
log_message "${YELLOW}=== Processing SFW Configuration Templates ===${NC}"

# SFW Registry configuration
if [ -f "server/Sfw/src/sfwserver/sfwregistry/sfwregistry.conf" ]; then
    process_template "server/Sfw/src/sfwserver/sfwregistry/sfwregistry.conf" "server/Sfw/src/sfwserver/sfwregistry/sfwregistry.conf.processed"
fi

# SFW Control configuration
if [ -f "server/Sfw/src/sfwserver/sfwcontrol/sfwcontrol.conf" ]; then
    process_template "server/Sfw/src/sfwserver/sfwcontrol/sfwcontrol.conf" "server/Sfw/src/sfwserver/sfwcontrol/sfwcontrol.conf.processed"
fi

# Process GM Tool configurations
log_message "${YELLOW}=== Processing GM Tool Configurations ===${NC}"

if [ -f "gmtool.snk.com/gmtool/snk.gmtool.com/phpinc/MTTDConfig.php" ]; then
    log_message "Processing GM Tool configuration..."
    
    # Create backup
    cp "gmtool.snk.com/gmtool/snk.gmtool.com/phpinc/MTTDConfig.php" \
       "gmtool.snk.com/gmtool/snk.gmtool.com/phpinc/MTTDConfig.php.backup"
    
    # Update IP addresses in GM tool config
    sed -i "s/127\.0\.0\.1/$NEW_SERVER_IP/g" "gmtool.snk.com/gmtool/snk.gmtool.com/phpinc/MTTDConfig.php"
    
    # Update database password
    sed -i "s/295d7ca8212bddb1/$DB_PASSWORD/g" "gmtool.snk.com/gmtool/snk.gmtool.com/phpinc/MTTDConfig.php"
    
    log_message "${GREEN}✓ GM Tool configuration updated${NC}"
fi

# Validation phase
log_message "${YELLOW}=== Validation Phase ===${NC}"

validation_errors=0

# Check for remaining template variables
log_message "Checking for unprocessed template variables..."
if find server -name "*.conf" -exec grep -l "TEMPLATE_" {} \; | head -5 | while read file; do
    log_message "${RED}ERROR: Unprocessed templates in $file${NC}"
    validation_errors=$((validation_errors + 1))
done; then
    if [ $validation_errors -gt 0 ]; then
        log_message "${RED}Found $validation_errors files with unprocessed templates${NC}"
    else
        log_message "${GREEN}✓ No unprocessed template variables found${NC}"
    fi
fi

# CRITICAL: Check for dangerous 0.0.0.0 configurations
log_message "Checking for dangerous 0.0.0.0 configurations..."
dangerous_configs=0

# Check sfwagent HostIP
if find server -name "sfwagent.conf" -exec grep -q "HostIP = 0.0.0.0" {} \; 2>/dev/null; then
    log_message "${RED}CRITICAL ERROR: Found HostIP = 0.0.0.0 in sfwagent.conf - This will break SFW Framework!${NC}"
    dangerous_configs=$((dangerous_configs + 1))
fi

# Check SFW registry endpoints
if find server -name "*.sfw.conf" -exec grep -q "tcp -h 0.0.0.0 -p 200[0-2]" {} \; 2>/dev/null; then
    log_message "${RED}CRITICAL ERROR: Found SFW services binding to 0.0.0.0 - This will break service discovery!${NC}"
    dangerous_configs=$((dangerous_configs + 1))
fi

if [ $dangerous_configs -eq 0 ]; then
    log_message "${GREEN}✓ No dangerous 0.0.0.0 configurations found${NC}"
else
    log_message "${RED}Found $dangerous_configs dangerous configurations - MUST FIX BEFORE DEPLOYMENT${NC}"
    validation_errors=$((validation_errors + dangerous_configs))
fi

# Check for correct SFW configurations
log_message "Validating SFW Framework configurations..."
sfw_config_errors=0

# Verify registry endpoint uses localhost
if find server -name "*.sfw.conf" -exec grep -q "tcp -h 127.0.0.1 -p 2000" {} \; 2>/dev/null; then
    log_message "${GREEN}✓ SFW Registry endpoint correctly configured (127.0.0.1:2000)${NC}"
else
    log_message "${RED}ERROR: SFW Registry endpoint not found or incorrectly configured${NC}"
    sfw_config_errors=$((sfw_config_errors + 1))
fi

# Verify external client port uses 0.0.0.0
if find server -name "*.sfw.conf" -exec grep -q "tcp -h 0.0.0.0 -p 18101" {} \; 2>/dev/null; then
    log_message "${GREEN}✓ External client port correctly configured (0.0.0.0:18101)${NC}"
else
    log_message "${YELLOW}WARNING: External client port (18101) may not be configured for external access${NC}"
fi

validation_errors=$((validation_errors + sfw_config_errors))

# Generate configuration summary
log_message "${YELLOW}=== Configuration Summary ===${NC}"

cat > "deployment_config_summary.txt" << EOF
SNK Game Server Deployment Configuration Summary
Generated: $(date)

Server Configuration:
- Server IP: $NEW_SERVER_IP
- Database User: $DB_USER
- Game ID: $GAME_ID
- Zone ID: $ZONE_ID
- Gate ID: $GATE_ID
- Zone Division: $ZONE_DIVISION

Service Endpoints:
- SFW Registry: tcp -h $NEW_SERVER_IP -p 2000
- Game Service: tcp -h $NEW_SERVER_IP -p 8201
- Login Service: tcp -h $NEW_SERVER_IP -p 7001
- Account Service: tcp -h $NEW_SERVER_IP -p 7011

Database Configuration:
- MySQL Host: $NEW_SERVER_IP:3306
- Registry DB: db_snk
- IAP DB: db_iap
- Game DB: db_zone_$ZONE_ID

Redis Configuration:
- Redis Host: $NEW_SERVER_IP:6379
- Authentication: Enabled

Files Processed:
$(find server -name "*.conf" -newer "$(date -d '1 minute ago' '+%Y-%m-%d %H:%M:%S')" | wc -l) configuration files updated

Next Steps:
1. Review processed configuration files
2. Deploy configurations to target server
3. Start services using /data/system_start.sh
4. Verify deployment using /data/game_status_dashboard.sh
EOF

log_message "${GREEN}Configuration summary saved to: deployment_config_summary.txt${NC}"

# Final status
log_message "${BLUE}========================================${NC}"
if [ $validation_errors -eq 0 ]; then
    log_message "${GREEN}✅ Configuration processing completed successfully!${NC}"
    log_message "${GREEN}   All template variables have been processed${NC}"
    log_message "${BLUE}Next steps:${NC}"
    log_message "1. Review the processed configuration files"
    log_message "2. Deploy to target server"
    log_message "3. Start services and verify deployment"
else
    log_message "${RED}⚠ Configuration processing completed with warnings${NC}"
    log_message "${RED}   Please review and fix unprocessed template variables${NC}"
fi
log_message "${BLUE}========================================${NC}"
