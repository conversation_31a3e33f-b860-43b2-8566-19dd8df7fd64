# 🚀 SNK Game Server - aaPanel Deployment Quick Start

## Server Information
- **Server IP**: **************
- **Control Panel**: aaPanel (pre-installed MySQL, Redis, PHP)
- **MySQL Root Password**: 6eaef66fd7ed44fa
- **SSH Access**: Configured with SSH keys

---

## 📋 **Pre-Deployment Checklist**

### 1. **Local Environment Setup**
```bash
# Ensure you're in the project directory
cd /path/to/snk-game-server

# Make scripts executable
chmod +x *.sh

# Verify required files exist
ls -la config_processor.sh deploy_to_aapanel_server.sh validate_aapanel_environment.sh
```

### 2. **Validate Target Server**
```bash
# Run environment validation
./validate_aapanel_environment.sh

# Expected: All critical services should be available
# MySQL, Redis, PHP should be pre-installed via aaPanel
```

---

## 🚀 **Deployment Steps**

### Step 1: Environment Validation
```bash
./validate_aapanel_environment.sh
```

**Expected Results:**
- ✅ SSH connectivity successful
- ✅ MySQL service running (port 3306)
- ✅ Redis service running (port 6379)
- ✅ PHP installed and configured
- ✅ Required ports available

### Step 2: Run Deployment
```bash
./deploy_to_aapanel_server.sh
```

**What this script does:**
1. **Process Configuration Templates** - Updates all config files with correct IPs
2. **Setup Database** - Creates game databases and user accounts
3. **Import Database Schemas** - Imports game data with IP corrections
4. **Configure Redis** - Sets up Redis for game server use
5. **Deploy Files** - Copies all server files and scripts
6. **Configure Firewall** - Opens required game server ports
7. **Validate Deployment** - Checks everything is properly configured

### Step 3: Start Services
```bash
# Connect to server
ssh root@**************

# Start SFW Framework first
cd /data/sfw
./start_manual.sh

# Wait for SFW to fully start, then start game servers
cd /data/SNK
./start_manual.sh

# Check system status
/data/game_status_dashboard.sh
```

---

## 🔧 **Configuration Details**

### **Database Configuration**
- **MySQL Root**: root / 6eaef66fd7ed44fa
- **Game User**: snk_user / snk_game_2024
- **Databases**: db_snk, db_iap, db_zone_1, db_zone_101

### **Redis Configuration**
- **Password**: redis_snk_2024
- **Binding**: 127.0.0.1 + **************
- **Port**: 6379

### **Game Server Configuration**
- **Server IP**: **************
- **Game ID**: 101
- **Zone ID**: 101
- **SFW Services**: 127.0.0.1 (localhost only)
- **External Client Port**: 0.0.0.0:18101

---

## 🔍 **Verification Commands**

### **Check Services Status**
```bash
# On server (ssh root@**************)

# Check SFW Framework
netstat -tuln | grep -E "(2000|2001|2002)"
# Expected: 3 ports listening on 127.0.0.1

# Check Game Servers
netstat -tuln | grep -E "(8101|8201|8301)"
# Expected: Game server ports listening

# Check Database
mysql -u snk_user -p'snk_game_2024' -e "SHOW DATABASES;"
# Expected: db_snk, db_iap, db_zone_1, db_zone_101

# Check Redis
redis-cli -a 'redis_snk_2024' ping
# Expected: PONG
```

### **Check Processes**
```bash
# SFW Framework processes
ps aux | grep -E "(sfw|SFW)" | grep -v grep

# Game server processes
ps aux | grep -E "(Server|server)" | grep -v grep

# System status dashboard
/data/game_status_dashboard.sh
```

---

## 🚨 **Troubleshooting**

### **Common Issues**

#### **1. SSH Connection Failed**
```bash
# Check SSH key
ssh-add -l

# Test connection manually
ssh -v root@**************
```

#### **2. MySQL Connection Failed**
```bash
# On server, check MySQL status
systemctl status mysql

# Test root connection
mysql -u root -p'6eaef66fd7ed44fa' -e "SELECT VERSION();"
```

#### **3. SFW Framework Won't Start**
```bash
# Check configuration
grep "HostIP" /data/sfw/sfwagent/sfwagent.conf
# Expected: HostIP = 127.0.0.1 (NOT 0.0.0.0)

# Check database registry
mysql -u snk_user -p'snk_game_2024' db_snk -e "SELECT server, node FROM t_service WHERE server LIKE 'sfw%';"
# Expected: All should show node = '127.0.0.1'
```

#### **4. External Client Can't Connect**
```bash
# Check external port binding
netstat -tuln | grep 18101
# Expected: 0.0.0.0:18101 (not 127.0.0.1:18101)

# Test from external machine
nc -zv ************** 18101
# Expected: Connection succeeded
```

### **Emergency Recovery**
```bash
# Stop all services
cd /data && ./system_stop.sh

# Reset database (if needed)
mysql -u root -p'6eaef66fd7ed44fa' -e "DROP DATABASE db_snk; DROP DATABASE db_iap; DROP DATABASE db_zone_1; DROP DATABASE db_zone_101;"

# Re-run deployment
./deploy_to_aapanel_server.sh
```

---

## 📊 **Port Reference**

### **SFW Framework (Internal Only)**
- 2000: SFW Registry (127.0.0.1)
- 2001: SFW Control (127.0.0.1)
- 2002: SFW Agent (127.0.0.1)

### **Core Game Services**
- 7011: Account Server
- 7021: Forward Server
- 7031: Platform Server
- 7041: Match Server
- 7051: Play Server
- 7061: Rank Server
- 7071: Global Server
- 7081: Team Server
- 7091: Friend Server
- 7101: IAP Server

### **Zone Services**
- 8101: Gate Server (Internal)
- 8201: Game Server
- 8301: Battle Server
- 18101: Gate Server (External Client Access)

### **System Services**
- 3306: MySQL Database
- 6379: Redis Cache

---

## 📝 **Post-Deployment Tasks**

### **1. Security Hardening**
```bash
# Change default passwords
# Update firewall rules
# Configure SSL certificates (if needed)
```

### **2. Monitoring Setup**
```bash
# Setup log rotation
# Configure monitoring alerts
# Setup backup procedures
```

### **3. Performance Tuning**
```bash
# Optimize MySQL configuration
# Tune Redis settings
# Adjust game server parameters
```

---

## 🎯 **Success Criteria**

### **Deployment Successful When:**
- ✅ All SFW Framework services running (ports 2000-2002)
- ✅ All game servers running (ports 8101, 8201, 8301)
- ✅ Database connectivity confirmed
- ✅ Redis connectivity confirmed
- ✅ External client port accessible (18101)
- ✅ No critical errors in logs

### **Ready for Production When:**
- ✅ All services stable for 30+ minutes
- ✅ Game functionality tested
- ✅ Performance within acceptable limits
- ✅ Monitoring and alerting configured
- ✅ Backup procedures tested

---

## 📞 **Support**

### **Log Locations**
- SFW Framework: `/data/applog/sfw/`
- Game Servers: `/data/applog/SNK/`
- System Logs: `/data/applog/system_startup.log`

### **Key Commands**
```bash
# System status
/data/game_status_dashboard.sh

# Start system
/data/system_start.sh

# Stop system
/data/system_stop.sh

# View logs
tail -f /data/applog/SNK/*
```

---

## ✅ **Quick Deployment Summary**

```bash
# 1. Validate environment
./validate_aapanel_environment.sh

# 2. Deploy to server
./deploy_to_aapanel_server.sh

# 3. Start services on server
ssh root@************** 'cd /data/sfw && ./start_manual.sh'
ssh root@************** 'cd /data/SNK && ./start_manual.sh'

# 4. Verify deployment
ssh root@************** '/data/game_status_dashboard.sh'
```

**Total deployment time: ~10-15 minutes**

🎉 **Your SNK Game Server should now be running on aaPanel!**
